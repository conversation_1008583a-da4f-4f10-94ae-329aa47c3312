"""
测试数据清理功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import os

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

from interactive_financial_generator import InteractiveFinancialGenerator

def create_test_excel_with_nan():
    """创建包含NaN和异常数据的测试Excel文件"""
    
    # 创建测试目录
    test_dir = Path("test_data_cleaning")
    test_dir.mkdir(exist_ok=True)
    
    # 创建包含各种异常数据的DataFrame
    test_data = {
        '项目': [
            '流动资产',
            '货币资金',
            '应收账款',
            '存货',
            '其他流动资产',
            '流动资产合计',
            '',  # 空字符串
            '非流动资产',
            '固定资产',
            '无形资产',
            '长期投资',
            '非流动资产合计',
            '',
            '资产总计'
        ],
        '2023年12月31日': [
            np.nan,  # NaN值
            1250000000,
            2100000000,
            np.inf,  # 无穷大
            280000000,
            5430000000,
            '',  # 空字符串
            np.nan,
            3200000000,
            -np.inf,  # 负无穷大
            1800000000,
            10120000000,
            None,  # None值
            15550000000
        ],
        '2022年12月31日': [
            '',
            1100000000,
            1850000000,
            380000000,
            250000000,
            4740000000,
            np.nan,
            '',
            2900000000,
            950000000,
            1650000000,
            9190000000,
            '',
            13930000000
        ]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    excel_file = test_dir / "测试数据_含异常值.xlsx"
    df.to_excel(excel_file, index=False, sheet_name="资产负债表")
    
    print(f"✅ 创建测试Excel文件: {excel_file}")
    print(f"📊 原始数据包含: NaN值、无穷大值、None值、空字符串")
    
    return excel_file, test_dir

def test_data_cleaning():
    """测试数据清理功能"""
    print("🧪 数据清理功能测试")
    print("=" * 50)
    
    # 创建测试数据
    excel_file, test_dir = create_test_excel_with_nan()
    
    # 创建生成器实例
    generator = InteractiveFinancialGenerator()
    generator.work_dir = test_dir
    
    # 测试读取和清理
    print(f"\n📄 测试读取Excel文件...")
    excel_data = generator.read_excel_file(excel_file)
    
    if excel_data:
        print(f"✅ 文件读取成功")
        
        # 显示清理后的文本
        print(f"\n📝 清理后的文本内容:")
        print("-" * 40)
        clean_text = excel_data.get('clean_text', '')
        print(clean_text[:1000] + "..." if len(clean_text) > 1000 else clean_text)
        
        # 保存清理后的文本到文件
        text_file = test_dir / "清理后的数据.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(clean_text)
        
        print(f"\n💾 清理后的完整数据已保存到: {text_file}")
        
        # 检查是否成功清理了异常值
        if 'nan' not in clean_text.lower() and 'inf' not in clean_text.lower():
            print("✅ 异常值清理成功：未发现NaN、inf等异常数据")
        else:
            print("⚠️  可能仍存在异常值，请检查清理逻辑")
        
        return True
    else:
        print("❌ 文件读取失败")
        return False

def test_number_formatting():
    """测试数值格式化功能"""
    print(f"\n🔢 测试数值格式化...")
    
    # 创建生成器实例
    generator = InteractiveFinancialGenerator()
    
    # 创建测试DataFrame
    test_df = pd.DataFrame({
        '项目': ['测试项目1', '测试项目2', '测试项目3', '测试项目4'],
        '金额': [1250000000, 50000000, 1500000, 800]  # 不同规模的数值
    })
    
    # 测试转换
    clean_text = generator.convert_to_clean_text(test_df, "测试工作表")
    
    print("转换结果:")
    print(clean_text)
    
    # 检查格式化结果
    if "亿" in clean_text and "万" in clean_text:
        print("✅ 数值格式化成功：大数值已转换为亿/万单位")
    else:
        print("⚠️  数值格式化可能有问题")

def cleanup_test_files():
    """清理测试文件"""
    test_dir = Path("test_data_cleaning")
    if test_dir.exists():
        import shutil
        shutil.rmtree(test_dir)
        print(f"🧹 已清理测试目录: {test_dir}")

def main():
    """主测试函数"""
    try:
        print("🚀 开始数据清理功能测试")
        print("=" * 60)
        
        # 测试数据清理
        success1 = test_data_cleaning()
        
        # 测试数值格式化
        test_number_formatting()
        
        if success1:
            print(f"\n🎉 数据清理功能测试完成！")
            print("💡 主要改进:")
            print("1. ✅ 自动清理NaN、inf、None等异常值")
            print("2. ✅ 数值格式化为易读格式（万元、亿元）")
            print("3. ✅ 生成干净的文本格式供AI处理")
            print("4. ✅ 保存清理后的数据便于调试")
        else:
            print(f"\n❌ 测试失败")
        
        # 询问是否清理测试文件
        choice = input(f"\n是否清理测试文件？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            cleanup_test_files()
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
