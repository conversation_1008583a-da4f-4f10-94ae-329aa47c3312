name: Release Agno

on:
  release:
    types: [published]

permissions:
  contents: read

jobs:
  build-release-agno:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip build

      - name: Copy README
        run: |
          cp README.md libs/agno/

      - name: Build package
        run: |
          cd libs/agno
          python -m build

      - name: Publish to TestPyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          user: __token__
          password: ${{ secrets.TEST_PYPI_API_TOKEN }}
          repository-url: https://test.pypi.org/legacy/
          packages-dir: libs/agno/dist

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages-dir: libs/agno/dist

  build-release-agno-aws:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip build

      - name: Build package
        run: |
          cd libs/infra/agno_aws
          python -m build

      - name: Publish to TestPyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        continue-on-error: true
        with:
          user: __token__
          password: ${{ secrets.TEST_PYPI_API_TOKEN }}
          repository-url: https://test.pypi.org/legacy/
          packages-dir: libs/infra/agno_aws/dist

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        continue-on-error: true
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages-dir: libs/infra/agno_aws/dist

  build-release-agno-docker:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip build

      - name: Build package
        run: |
          cd libs/infra/agno_docker
          python -m build

      - name: Publish to TestPyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        continue-on-error: true
        with:
          user: __token__
          password: ${{ secrets.TEST_PYPI_API_TOKEN }}
          repository-url: https://test.pypi.org/legacy/
          packages-dir: libs/infra/agno_docker/dist

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        continue-on-error: true
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages-dir: libs/infra/agno_docker/dist
