# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.6.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.49.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
anyio==4.8.0
    # via
    #   anthropic
    #   google-genai
    #   groq
    #   httpx
    #   mcp
    #   openai
    #   sse-starlette
    #   starlette
attrs==25.1.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   streamlit
    #   typer
    #   uvicorn
deprecation==2.1.0
    # via lancedb
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
docstring-parser==0.16
    # via agno
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
google-auth==2.38.0
    # via google-genai
google-genai==1.5.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
groq==0.18.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   google-genai
    #   groq
    #   mcp
    #   openai
httpx-sse==0.4.0
    # via mcp
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.8.2
    # via
    #   anthropic
    #   openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
lancedb==0.20.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mcp==1.3.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
mdurl==0.1.2
    # via markdown-it-py
narwhals==1.29.1
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
numpy==2.2.3
    # via
    #   pandas
    #   pydeck
    #   pylance
    #   streamlit
openai==1.65.5
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
overrides==7.7.0
    # via lancedb
packaging==24.2
    # via
    #   altair
    #   deprecation
    #   lancedb
    #   streamlit
pandas==2.2.3
    # via streamlit
pillow==11.1.0
    # via streamlit
protobuf==5.29.3
    # via streamlit
pyarrow==19.0.1
    # via
    #   pylance
    #   streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pydantic==2.10.6
    # via
    #   agno
    #   anthropic
    #   google-genai
    #   groq
    #   lancedb
    #   mcp
    #   openai
    #   pydantic-settings
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.8.1
    # via
    #   agno
    #   mcp
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pylance==0.23.2
    # via lancedb
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.0.1
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.1
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   google-genai
    #   streamlit
rich==13.9.4
    # via
    #   agno
    #   typer
rpds-py==0.23.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
sqlalchemy==2.0.38
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
sse-starlette==2.2.1
    # via mcp
starlette==0.46.1
    # via
    #   mcp
    #   sse-starlette
streamlit==1.43.1
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
tantivy==0.22.0
    # via -r cookbook/examples/apps/mcp_agent/requirements.in
tenacity==9.0.0
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via
    #   lancedb
    #   openai
typer==0.15.2
    # via agno
typing-extensions==4.12.2
    # via
    #   agno
    #   altair
    #   anthropic
    #   anyio
    #   google-genai
    #   groq
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
tzdata==2025.1
    # via pandas
urllib3==2.3.0
    # via requests
uvicorn==0.34.0
    # via mcp
websockets==14.2
    # via google-genai
