from agno.agent import Agent
from agno.models.anthropic import <PERSON>
from agno.playground import Playground, serve_playground_app
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.thinking import ThinkingTools
from agno.tools.yfinance import YFinanceTools

agent_storage: str = "tmp/agents.db"

thinking_web_agent = Agent(
    name="Thinking Web Agent",
    model=<PERSON>(id="claude-3-7-sonnet-latest"),
    tools=[ThinkingTools(add_instructions=True), DuckDuckGoTools()],
    instructions=["Always include sources"],
    # Store the agent sessions in a sqlite database
    storage=SqliteAgentStorage(table_name="web_agent", db_file=agent_storage),
    # Adds the current date and time to the instructions
    add_datetime_to_instructions=True,
    # Adds the history of the conversation to the messages
    add_history_to_messages=True,
    # Number of history responses to add to the messages
    num_history_responses=5,
    # Adds markdown formatting to the messages
    markdown=True,
)

thinking_finance_agent = Agent(
    name="Thinking Finance Agent",
    model=<PERSON>(id="claude-3-7-sonnet-latest"),
    tools=[
        ThinkingTools(add_instructions=True),
        YFinanceTools(
            stock_price=True,
            analyst_recommendations=True,
            company_info=True,
            company_news=True,
        ),
    ],
    instructions="Use tables to display data",
    storage=SqliteAgentStorage(table_name="finance_agent", db_file=agent_storage),
    add_datetime_to_instructions=True,
    add_history_to_messages=True,
    num_history_responses=5,
    markdown=True,
)

playground = Playground(
    agents=[thinking_web_agent, thinking_finance_agent],
    name="Thinking Playground",
    description="A playground for thinking",
    app_id="thinking-playground",
)
app = playground.get_app()

if __name__ == "__main__":
    playground.serve(app="thinking_playground:app", reload=True)
