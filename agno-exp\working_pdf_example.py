"""
工作的PDF知识库示例 - 不使用向量数据库
直接将PDF内容嵌入到Agent的指令中
"""

import os
from textwrap import dedent

from agno.agent import Agent
from agno.knowledge.pdf import PDFReader
from agno.models.ollama import Ollama

def load_pdf_content(pdf_path: str, max_chunks: int = 10):
    """
    加载PDF内容
    
    Args:
        pdf_path: PDF文件路径
        max_chunks: 最大文档块数量
    
    Returns:
        合并后的PDF内容字符串
    """
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return None
    
    try:
        print(f"📄 正在读取PDF: {pdf_path}")
        
        # 创建PDF读取器
        reader = PDFReader(chunk=True, chunk_size=2000)
        documents = reader.read(pdf=pdf_path)
        
        print(f"✅ 成功读取 {len(documents)} 个文档块")
        
        # 取前N个文档块并合并
        selected_docs = documents[:max_chunks]
        content = "\n\n".join([doc.content for doc in selected_docs if doc.content.strip()])
        
        print(f"📊 合并后内容长度: {len(content)} 字符")
        
        return content
        
    except Exception as e:
        print(f"❌ PDF读取失败: {e}")
        return None

def create_pdf_agent(pdf_content: str, ollama_host: str = "http://*************:11434"):
    """
    创建基于PDF内容的Agent
    
    Args:
        pdf_content: PDF文档内容
        ollama_host: Ollama服务地址
    
    Returns:
        配置好的Agent实例
    """
    
    try:
        # 配置Ollama模型
        model = Ollama(
            id="qwen3:0.6b",
            host=ollama_host,
            options={
                "temperature": 0.1,  # 降低温度获得更准确回答
                "top_p": 0.9,
                "num_ctx": 4096,  # 增加上下文长度
            }
        )
        
        # 创建Agent，将PDF内容嵌入指令中
        agent = Agent(
            model=model,
            instructions=dedent(f"""\
                你是一个专业的装备管理文档助手，专门回答基于装备平台和装备条线操作手册的问题。
                
                以下是你的知识库内容（装备管理操作手册）：
                
                ===== 文档内容开始 =====
                {pdf_content[:8000]}  # 限制内容长度避免超出上下文
                ===== 文档内容结束 =====
                
                工作原则：
                1. 仅基于上述文档内容回答问题
                2. 如果问题超出文档范围，明确告知用户"抱歉，该问题超出了操作手册的范围"
                3. 回答要准确、详细，并尽可能引用文档中的具体内容
                4. 保持专业和友好的语调
                5. 使用专业术语，提供清晰、有条理的回答
                
                回答格式：
                - 引言："您好，关于您提出的装备平台/装备条线问题，我根据操作手册为您提供以下回答："
                - 主体内容：详细描述操作步骤或管理流程，强调关键节点和注意事项
                - 总结："希望以上回答能对您有所帮助。如有其他问题或需要更多信息，请随时告诉我。"
            """),
            markdown=True,
        )
        
        return agent
        
    except Exception as e:
        print(f"❌ Agent创建失败: {e}")
        return None

def test_pdf_qa(agent):
    """测试PDF问答功能"""
    
    questions = [
        "如何访问装备系统?",
        "车型规则怎么设置?",
        "车型组合测算怎么发布?",
        "装备平台的主要功能有哪些？",
        "操作手册的版本信息是什么？"
    ]
    
    print("\n📝 开始PDF问答测试")
    print("=" * 60)
    
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. 问题: {question}")
        print("-" * 40)
        
        try:
            # 使用run方法获取完整回答
            response = agent.run(question)
            print(f"🤖 回答: {response.content}")
            
        except Exception as e:
            print(f"❌ 回答失败: {e}")
        
        print()

def interactive_mode(agent):
    """交互模式"""
    print("\n🎮 进入交互模式")
    print("输入问题，Agent将基于PDF文档回答")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 60)
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 AI助手:")
            response = agent.run(user_input)
            print(response.content)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print("📚 装备管理PDF知识库系统")
    print("=" * 60)
    
    # 1. 加载PDF内容
    pdf_path = "agno-exp/pur.pdf"
    pdf_content = load_pdf_content(pdf_path, max_chunks=8)
    
    if not pdf_content:
        print("❌ 无法加载PDF内容，程序退出")
        return
    
    # 2. 创建Agent
    print("\n🤖 创建PDF知识库Agent...")
    agent = create_pdf_agent(pdf_content)
    
    if not agent:
        print("❌ 无法创建Agent，程序退出")
        return
    
    print("✅ Agent创建成功")
    
    # 3. 运行测试
    test_pdf_qa(agent)
    
    # 4. 可选：启动交互模式
    print("\n" + "=" * 60)
    choice = input("是否启动交互模式？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        interactive_mode(agent)

if __name__ == "__main__":
    main()
