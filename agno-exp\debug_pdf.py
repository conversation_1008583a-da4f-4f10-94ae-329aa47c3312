"""
调试PDF功能
"""

import os
import sys

print("🔍 开始调试...")

# 1. 检查PDF文件
pdf_path = "agno-exp/pur.pdf"
print(f"1. 检查PDF文件: {pdf_path}")

if os.path.exists(pdf_path):
    size = os.path.getsize(pdf_path)
    print(f"   ✅ 文件存在，大小: {size} 字节")
else:
    print(f"   ❌ 文件不存在")
    sys.exit(1)

# 2. 测试PDF读取
print("2. 测试PDF读取...")
try:
    from agno.knowledge.pdf import PDFReader
    
    reader = PDFReader(chunk=True, chunk_size=1000)
    print("   ✅ PDFReader创建成功")
    
    documents = reader.read(pdf=pdf_path)
    print(f"   ✅ PDF读取成功，共 {len(documents)} 个文档块")
    
    if documents:
        first_content = documents[0].content[:200]
        print(f"   📝 第一块内容: {first_content}...")
    
except Exception as e:
    print(f"   ❌ PDF读取失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 3. 测试Ollama模型
print("3. 测试Ollama模型...")
try:
    from agno.models.ollama import Ollama
    
    model = Ollama(
        id="qwen3:0.6b",
        host="http://*************:11434",
    )
    print("   ✅ Ollama模型创建成功")
    
except Exception as e:
    print(f"   ❌ Ollama模型创建失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 4. 测试Agent创建
print("4. 测试Agent创建...")
try:
    from agno.agent import Agent
    
    agent = Agent(
        model=model,
        instructions="你是一个测试助手。请简短回答问题。"
    )
    print("   ✅ Agent创建成功")
    
except Exception as e:
    print(f"   ❌ Agent创建失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 5. 测试简单对话
print("5. 测试简单对话...")
try:
    response = agent.run("请回复'测试成功'")
    print(f"   ✅ 对话成功: {response.content}")
    
except Exception as e:
    print(f"   ❌ 对话失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 所有测试通过！")
print("现在可以创建完整的PDF知识库系统了。")
