"""
最简化的PDF测试 - 验证基本功能
"""

import os
from textwrap import dedent

from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.models.ollama import Ollama

def test_basic_pdf():
    """测试基本PDF功能"""
    
    pdf_path = "agno-exp/pur.pdf"
    
    # 检查PDF文件
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    print(f"✅ 找到PDF文件: {pdf_path}")
    print(f"📊 文件大小: {os.path.getsize(pdf_path)} 字节")
    
    try:
        # 1. 测试PDF读取
        print("\n🔄 测试PDF读取...")
        reader = PDFReader(chunk=True, chunk_size=1000)
        documents = reader.read(pdf=pdf_path)
        
        print(f"✅ PDF读取成功，共 {len(documents)} 个文档块")
        
        if documents:
            first_doc = documents[0]
            print(f"📝 第一块内容预览: {first_doc.content[:200]}...")
        
        # 2. 测试Ollama连接
        print("\n🔄 测试Ollama连接...")
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        # 简单测试
        test_agent = Agent(
            model=model,
            instructions="你是一个测试助手。"
        )
        
        response = test_agent.run("请回复'连接成功'")
        print(f"✅ Ollama连接成功: {response.content}")
        
        # 3. 测试不使用向量数据库的PDF Agent
        print("\n🔄 创建简单PDF Agent...")
        
        # 合并所有文档内容
        all_content = "\n\n".join([doc.content for doc in documents[:5]])  # 只取前5个文档块
        
        pdf_agent = Agent(
            model=model,
            instructions=dedent(f"""\
                你是一个专业的文档助手。以下是PDF文档的内容：
                
                {all_content[:3000]}...
                
                请基于上述内容回答用户问题。如果问题超出文档范围，请明确告知。
            """),
            markdown=True,
        )
        
        print("✅ PDF Agent创建成功")
        
        # 4. 测试问答
        print("\n🔄 测试问答功能...")
        
        test_questions = [
            "这个文档的主要内容是什么？",
            "如何访问装备系统？"
        ]
        
        for question in test_questions:
            print(f"\n❓ 问题: {question}")
            try:
                response = pdf_agent.run(question)
                print(f"🤖 回答: {response.content[:300]}...")
            except Exception as e:
                print(f"❌ 回答失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 最简化PDF测试")
    print("=" * 50)
    
    success = test_basic_pdf()
    
    if success:
        print("\n✅ 所有测试通过！")
        print("💡 可以尝试运行完整的PDF知识库示例")
    else:
        print("\n❌ 测试失败，请检查配置")

if __name__ == "__main__":
    main()
