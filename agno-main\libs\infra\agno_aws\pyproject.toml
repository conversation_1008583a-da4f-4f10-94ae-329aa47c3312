[project]
name = "agno-aws"
version = "0.0.2"
description = "AWS resources for Agno"
requires-python = ">=3.7"
readme = "README.md"
authors = [
  {name = "Ash<PERSON><PERSON>i", email = "<EMAIL>"}
]

dependencies = [
  "boto3",
]

[project.optional-dependencies]
dev = [
  "mypy",
  "pytest",
  "ruff",
]

[project.urls]
homepage = "https://agno.com"
documentation = "https://docs.agno.com"

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = ["agno*"]

[tool.setuptools.package-data]
agno = ["py.typed"]

[tool.pytest.ini_options]
log_cli = true
testpaths = "tests"

[tool.ruff]
line-length = 120
# Ignore `F401` (import violations) in all `__init__.py` files
[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
check_untyped_defs = true
no_implicit_optional = true
warn_unused_configs = true
plugins = ["pydantic.mypy"]

[[tool.mypy.overrides]]
module = [
  "agno.*",
  "boto3.*",
  "botocore.*",
]
ignore_missing_imports = true
