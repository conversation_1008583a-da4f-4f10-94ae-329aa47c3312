# 上市公司财报数据HTML页面生成系统

基于Agno框架和Ollama模型的智能财报HTML生成工具，能够自动读取Excel财报数据并生成美观的动态网页。

## 🚀 功能特性

- **智能数据解析**: 自动读取Excel格式的资产负债表数据
- **AI驱动生成**: 使用大模型分析财报数据并生成HTML页面
- **现代化设计**: 集成TailwindCSS、GSAP动效和响应式布局
- **可定制提示词**: 提示词单独存储，便于调整和优化
- **批量处理**: 支持处理多个Excel文件
- **专业展示**: 包含关键指标、图表可视化和财务比率分析

## 📁 项目结构

```
agno-exp/
├── financial_report_generator.py    # 完整版生成器
├── simple_financial_generator.py    # 简化版生成器（推荐）
├── create_sample_data.py           # 示例数据创建工具
├── financial_prompt.txt            # 大模型提示词文件
├── financial_data/                 # Excel文件输入目录
├── html_reports/                   # HTML文件输出目录
└── FINANCIAL_README.md            # 本说明文件
```

## 🛠️ 安装依赖

```bash
# 基础依赖（已安装）
pip install agno ollama

# Excel处理依赖
pip install pandas openpyxl
```

## 📊 使用方法

### 方法1：快速演示（推荐）

```bash
# 运行简化版演示
python agno-exp/simple_financial_generator.py
```

这将：
1. 创建示例财报数据
2. 使用AI生成HTML页面
3. 保存到 `html_reports/demo_financial_report.html`

### 方法2：处理Excel文件

1. **准备Excel文件**
   ```bash
   # 创建示例Excel文件
   python agno-exp/create_sample_data.py
   ```

2. **将Excel文件放入输入目录**
   ```
   agno-exp/financial_data/
   ├── 公司A_资产负债表_2023.xlsx
   ├── 公司B_资产负债表_2023.xlsx
   └── ...
   ```

3. **运行完整版生成器**
   ```bash
   python agno-exp/financial_report_generator.py
   ```

### 方法3：自定义提示词

1. **编辑提示词文件**
   ```bash
   # 编辑 agno-exp/financial_prompt.txt
   # 根据需要调整设计风格、技术要求等
   ```

2. **重新运行生成器**
   ```bash
   python agno-exp/simple_financial_generator.py
   ```

## 📋 Excel数据格式要求

### 支持的文件格式
- `.xlsx` (推荐)
- `.xls`

### 数据结构示例
```
项目                    2023年12月31日    2022年12月31日
流动资产
货币资金                1,250,000,000     1,100,000,000
应收账款                2,100,000,000     1,850,000,000
存货                      450,000,000       380,000,000
...
```

### 关键要求
- 第一列为项目名称
- 后续列为不同期间的数据
- 支持多个工作表（资产负债表、利润表、现金流量表等）
- 数值格式：支持数字、千分位分隔符

## 🎨 生成的HTML特性

### 技术栈
- **HTML5** + **TailwindCSS**
- **GSAP动效** + **Framer Motion**
- **Chart.js** 或 **D3.js** 图表
- **响应式设计**

### 页面结构
1. **页面标题区域** - 公司名称和报告期间
2. **核心指标仪表板** - 关键财务数据卡片
3. **资产负债表可视化** - 资产和负债结构图
4. **财务比率分析** - 各类财务指标
5. **趋势分析图表** - 历史数据对比
6. **数据详情表格** - 完整的财务数据

### 设计特点
- **现代化UI**: 深蓝色主题，简洁专业
- **数据可视化**: 图表展示财务趋势
- **响应式布局**: 适配桌面和移动设备
- **动画效果**: 平滑过渡和加载动画
- **中英文混排**: 大字体突出重点

## ⚙️ 配置选项

### 模型配置
```python
# 在生成器中可以调整
model = Ollama(
    id="qwen3:0.6b",                    # 模型名称
    host="http://*************:11434",  # Ollama服务地址
    options={
        "temperature": 0.2,              # 创造性控制
        "top_p": 0.9,                   # 多样性控制
        "num_ctx": 4096,                # 上下文长度
    }
)
```

### 目录配置
```python
generator = FinancialReportGenerator(
    input_dir="agno-exp/financial_data",    # Excel输入目录
    output_dir="agno-exp/html_reports",     # HTML输出目录
    prompt_file="agno-exp/financial_prompt.txt"  # 提示词文件
)
```

## 🔧 故障排除

### 常见问题

1. **模型连接失败**
   ```bash
   # 检查Ollama服务状态
   curl http://*************:11434/api/tags
   ```

2. **Excel读取失败**
   ```bash
   # 安装Excel处理库
   pip install pandas openpyxl xlrd
   ```

3. **HTML生成质量不佳**
   - 调整提示词文件 `financial_prompt.txt`
   - 降低模型temperature参数
   - 增加上下文长度num_ctx

4. **中文显示问题**
   - 确保文件编码为UTF-8
   - 检查HTML中的charset设置

### 调试模式
```python
# 在生成器中启用调试
agent = Agent(
    model=model,
    instructions=prompt,
    debug_mode=True,  # 启用调试输出
)
```

## 📈 示例输出

生成的HTML页面将包含：

- 📊 **财务指标卡片**: 总资产、净利润、资产负债率等
- 📈 **趋势图表**: 收入增长、利润变化等
- 🏢 **公司概览**: 基本信息和报告期间
- 💰 **资产结构**: 流动资产vs非流动资产占比
- 📋 **详细数据表**: 完整的财务数据展示

## 🤝 扩展开发

### 添加新的图表类型
1. 修改 `financial_prompt.txt`
2. 添加图表库引用（Chart.js、D3.js等）
3. 指定数据可视化要求

### 支持更多财报类型
1. 扩展Excel读取逻辑
2. 更新提示词以处理新的数据结构
3. 调整HTML模板结构

### 集成其他数据源
1. 添加API数据获取功能
2. 支持CSV、JSON等格式
3. 实现实时数据更新

## 📄 许可证

本项目遵循MIT许可证。
