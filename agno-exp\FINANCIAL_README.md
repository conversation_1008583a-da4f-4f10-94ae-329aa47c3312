# 上市公司财报数据HTML页面生成系统

基于Agno框架和Ollama模型的智能财报HTML生成工具，能够自动读取Excel财报数据并生成美观的动态网页。

## 🚀 功能特性

- **智能数据解析**: 自动读取Excel格式的资产负债表数据
- **AI驱动生成**: 使用大模型分析财报数据并生成HTML页面
- **现代化设计**: 集成TailwindCSS、GSAP动效和响应式布局
- **可定制提示词**: 提示词单独存储，便于调整和优化
- **批量处理**: 支持处理多个Excel文件
- **专业展示**: 包含关键指标、图表可视化和财务比率分析

## 📁 项目结构

```
agno-exp/
├── financial_report_generator.py    # 完整版生成器
├── simple_financial_generator.py    # 简化版生成器（推荐）
├── create_sample_data.py           # 示例数据创建工具
├── financial_prompt.txt            # 大模型提示词文件
├── financial_data/                 # Excel文件输入目录
├── html_reports/                   # HTML文件输出目录
└── FINANCIAL_README.md            # 本说明文件
```

## 🛠️ 安装依赖

```bash
# 基础依赖（已安装）
pip install agno ollama

# Excel处理依赖
pip install pandas openpyxl
```

## 📊 使用方法

### 🚀 一键启动（推荐）

```bash
# 运行启动脚本，选择所需功能
python agno-exp/start_financial_generator.py
```

启动后会显示菜单，包含以下选项：
1. **演示版** - 快速体验，自动创建示例数据
2. **交互版** - 完整功能，处理用户Excel文件
3. **创建示例数据** - 生成测试用Excel文件
4. **基础测试** - 测试AI连接和功能
5. **帮助说明** - 详细使用指南

### 📁 目录设置

程序启动时会提示选择工作目录：

**选项1：使用默认目录**
```
当前目录/财报数据/
├── Excel文件（输入）
├── HTML文件（输出）
└── 财报提示词.txt
```

**选项2：自定义目录**
```
用户指定目录/
├── Excel文件（输入）
├── HTML文件（输出）
└── 财报提示词.txt
```

### 💻 Windows路径示例

```
# 绝对路径
C:\Users\<USER>\Documents\财报数据
D:\工作文件\财务分析\2023年报

# 相对路径
.\财报数据
..\上级目录\财报文件
```

### 📋 具体使用步骤

1. **运行启动脚本**
   ```bash
   python agno-exp/start_financial_generator.py
   ```

2. **选择功能**
   - 初次使用建议选择"演示版"
   - 实际使用选择"交互版"

3. **设置工作目录**
   - 程序会提示选择或输入目录路径
   - 支持Windows格式路径（如 `C:\Users\<USER>