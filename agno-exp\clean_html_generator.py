"""
干净HTML生成器 - 专门解决AI输出冗余文字的问题
"""

import os
import json
import pandas as pd
from pathlib import Path
from textwrap import dedent
import re

from agno.agent import Agent
from agno.models.ollama import Ollama

class CleanHTMLGenerator:
    """干净HTML生成器"""
    
    def __init__(self):
        self.work_dir = None
        self.agent = None
        
    def get_work_directory(self):
        """获取工作目录"""
        print("🧹 干净HTML生成器")
        print("=" * 40)
        
        print("\n请选择工作目录：")
        print("1. 使用当前目录下的 '财报演示' 文件夹")
        print("2. 输入自定义目录路径")
        
        while True:
            choice = input("\n请选择 (1-2): ").strip()
            
            if choice == "1":
                self.work_dir = Path.cwd() / "财报演示"
                break
            elif choice == "2":
                custom_path = input("请输入目录路径: ").strip()
                if custom_path:
                    self.work_dir = Path(custom_path).resolve()
                    break
                else:
                    print("❌ 路径不能为空")
            else:
                print("❌ 无效选择")
        
        # 创建目录
        try:
            self.work_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 工作目录: {self.work_dir}")
        except Exception as e:
            print(f"❌ 目录创建失败: {e}")
            raise
    
    def init_agent(self):
        """初始化AI Agent - 使用严格的提示词"""
        print("\n🤖 初始化AI模型...")
        
        try:
            model = Ollama(
                id="deepseek-r1:7b",
                host="http://*************:11434",
                options={
                    "temperature": 0.05,  # 极低温度确保输出稳定
                    "top_p": 0.8,
                    "num_ctx": 4096,
                }
            )
            
            # 极其严格的提示词
            prompt = dedent("""\
                你是HTML代码生成器。你的任务是根据财报数据生成HTML代码。
                
                严格要求：
                1. 只输出HTML代码，从<!DOCTYPE html>开始
                2. 不要输出任何解释、思考过程或说明文字
                3. 不要包含"好的"、"首先"、"接下来"等词语
                4. 不要包含markdown代码块标记
                5. 直接输出可用的HTML代码
                
                HTML要求：
                - 使用现代CSS样式
                - 响应式布局
                - 突出显示关键财务数据
                - 包含公司名称和报告期间
                - 使用卡片布局展示数据
                
                记住：只输出HTML代码，不要任何其他内容。
            """)
            
            self.agent = Agent(
                model=model,
                instructions=prompt,
                markdown=False,
            )
            
            print("✅ AI模型初始化成功")
            
        except Exception as e:
            print(f"❌ AI模型初始化失败: {e}")
            raise
    
    def clean_html_output(self, raw_output: str) -> str:
        """清理AI输出，提取纯HTML代码"""
        
        # 移除常见的AI思考过程开头
        patterns_to_remove = [
            r"好的，.*?(?=<!DOCTYPE|<html)",
            r"首先，.*?(?=<!DOCTYPE|<html)",
            r"接下来，.*?(?=<!DOCTYPE|<html)",
            r"用户.*?(?=<!DOCTYPE|<html)",
            r"我需要.*?(?=<!DOCTYPE|<html)",
            r"根据.*?(?=<!DOCTYPE|<html)",
            r"```html\s*",
            r"```\s*$",
        ]
        
        cleaned = raw_output
        for pattern in patterns_to_remove:
            cleaned = re.sub(pattern, "", cleaned, flags=re.DOTALL | re.IGNORECASE)
        
        # 查找HTML开始位置
        html_start = cleaned.find("<!DOCTYPE")
        if html_start == -1:
            html_start = cleaned.find("<html")
        
        if html_start != -1:
            cleaned = cleaned[html_start:]
        
        # 移除HTML结束后的内容
        html_end = cleaned.rfind("</html>")
        if html_end != -1:
            cleaned = cleaned[:html_end + 7]  # 包含</html>
        
        return cleaned.strip()
    
    def create_sample_excel(self):
        """创建示例Excel文件"""
        print("\n📊 创建示例Excel文件...")
        
        financial_data = {
            '项目': [
                '流动资产',
                '货币资金',
                '应收账款',
                '存货',
                '流动资产合计',
                '',
                '非流动资产',
                '固定资产',
                '无形资产',
                '非流动资产合计',
                '',
                '资产总计',
                '',
                '流动负债',
                '短期借款',
                '应付账款',
                '流动负债合计',
                '',
                '非流动负债',
                '长期借款',
                '非流动负债合计',
                '',
                '负债合计',
                '',
                '所有者权益',
                '股本',
                '未分配利润',
                '所有者权益合计',
                '',
                '负债和所有者权益总计'
            ],
            '2023年12月31日(万元)': [
                '',
                125000,
                210000,
                45000,
                380000,
                '',
                '',
                320000,
                110000,
                430000,
                '',
                810000,
                '',
                '',
                80000,
                120000,
                200000,
                '',
                '',
                150000,
                150000,
                '',
                350000,
                '',
                '',
                100000,
                360000,
                460000,
                '',
                810000
            ]
        }
        
        excel_file = self.work_dir / "测试公司_资产负债表_2023.xlsx"
        
        try:
            df = pd.DataFrame(financial_data)
            df.to_excel(excel_file, index=False, sheet_name="资产负债表")
            print(f"✅ 已创建示例Excel: {excel_file.name}")
            return excel_file
        except Exception as e:
            print(f"❌ Excel创建失败: {e}")
            return None
    
    def generate_clean_html(self, excel_file):
        """生成干净的HTML"""
        print(f"\n📄 处理Excel文件: {excel_file.name}")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            excel_data = df.to_string(index=False, max_rows=30)
            
            print("✅ Excel文件读取成功")
            
            # 生成HTML - 使用非常简洁的提示
            print("🤖 正在生成HTML...")
            
            simple_prompt = f"""
            财报数据：
            {excel_data}
            
            生成HTML页面，包含公司名称"测试科技公司"和报告期间"2023年度"。
            """
            
            response = self.agent.run(simple_prompt)
            raw_html = response.content
            
            print("🧹 清理HTML输出...")
            
            # 清理输出
            clean_html = self.clean_html_output(raw_html)
            
            print("✅ HTML生成和清理完成")
            
            # 保存HTML文件
            html_file = self.work_dir / f"{excel_file.stem}_干净版.html"
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(clean_html)
            
            print(f"✅ 干净HTML文件已保存: {html_file.name}")
            
            # 显示清理前后的对比
            print(f"\n📊 输出对比:")
            print(f"原始输出长度: {len(raw_html)} 字符")
            print(f"清理后长度: {len(clean_html)} 字符")
            
            if len(raw_html) > len(clean_html):
                print(f"✅ 成功清理了 {len(raw_html) - len(clean_html)} 个多余字符")
            
            return html_file
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return None
    
    def run_demo(self):
        """运行演示"""
        try:
            print("🧹 干净HTML生成器演示")
            print("=" * 50)
            
            # 1. 获取工作目录
            self.get_work_directory()
            
            # 2. 创建示例Excel文件
            excel_file = self.create_sample_excel()
            if not excel_file:
                return
            
            # 3. 初始化AI模型
            self.init_agent()
            
            # 4. 生成干净的HTML
            html_file = self.generate_clean_html(excel_file)
            
            if html_file:
                print(f"\n🎉 演示完成！")
                print(f"📁 工作目录: {self.work_dir}")
                print(f"📊 Excel文件: {excel_file.name}")
                print(f"🌐 HTML文件: {html_file.name}")
                print(f"\n💡 请在浏览器中打开HTML文件查看效果")
                print(f"🧹 此版本已清理所有AI思考过程，输出纯净HTML代码")
            else:
                print(f"\n❌ 演示失败")
                
        except KeyboardInterrupt:
            print(f"\n\n👋 用户中断程序")
        except Exception as e:
            print(f"\n❌ 演示失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    generator = CleanHTMLGenerator()
    generator.run_demo()

if __name__ == "__main__":
    main()
