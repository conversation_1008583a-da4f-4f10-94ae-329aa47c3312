"""
存储示例 - 使用Agno的存储功能
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.storage.agent_storage import AgentStorage
from textwrap import dedent

# 配置Ollama模型
ollama_model = Ollama(
    id="qwen3:0.6b",
    host="http://*************:11434",
)

# 创建带存储的Agent
agent = Agent(
    model=ollama_model,
    storage=AgentStorage(
        table_name="chat_sessions",
        db_file="tmp/agent_storage.db"
    ),
    instructions=dedent("""\
        你是一个智能助手，能够记住之前的对话内容。
        
        特点：
        1. 可以记住用户之前说过的话
        2. 能够基于历史对话提供连贯的回答
        3. 保持友好和专业的态度
        
        请用中文回答问题。
    """),
    markdown=True,
)

def test_storage():
    """测试存储功能"""
    print("💾 存储功能测试")
    print("=" * 40)
    
    # 创建会话
    session_id = "test_session_001"
    
    conversations = [
        "你好，我叫张三，是一名软件工程师。",
        "我正在学习人工智能相关的技术。",
        "你还记得我的名字吗？",
        "我的职业是什么？",
        "我们之前聊过什么话题？"
    ]
    
    for i, message in enumerate(conversations, 1):
        print(f"\n{i}. 用户: {message}")
        print("-" * 30)
        
        try:
            # 使用会话ID进行对话
            response = agent.run(message, session_id=session_id)
            print(f"AI: {response.content}")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print()

def show_storage_info():
    """显示存储信息"""
    print("\n📊 存储信息")
    print("-" * 20)
    
    try:
        if agent.storage:
            # 这里可以添加显示存储统计的代码
            print("✅ 存储功能已启用")
            print(f"📁 数据库文件: tmp/agent_storage.db")
            print(f"📋 表名: chat_sessions")
        else:
            print("❌ 存储功能未启用")
    except Exception as e:
        print(f"❌ 获取存储信息失败: {e}")

# 主执行逻辑
if __name__ == "__main__":
    print("💾 Agno存储功能示例")
    print("=" * 50)
    
    # 确保tmp目录存在
    import os
    os.makedirs("tmp", exist_ok=True)
    
    try:
        # 测试存储功能
        test_storage()
        
        # 显示存储信息
        show_storage_info()
        
        print("\n✅ 存储功能测试完成")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
