"""🧠 Agent with Storage - Your AI Thai Cooking Assistant!

This example shows how to create an AI cooking assistant that combines knowledge from a
curated recipe database with web searching capabilities. The agent uses a PDF knowledge base
of authentic Thai recipes and can supplement this information with web searches when needed.

Example prompts to try:
- "How do I make authentic Pad Thai?"
- "What's the difference between red and green curry?"
- "Can you explain what galangal is and possible substitutes?"
- "Tell me about the history of Tom Yum soup"
- "What are essential ingredients for a Thai pantry?"
- "How do I make Thai basil chicken (Pad Kra Pao)?"

Run `pip install openai lancedb tantivy pypdf duckduckgo-search sqlalchemy agno` to install dependencies.
"""

from textwrap import dedent
from typing import List, Optional

import typer
from agno.agent import Agent
from agno.document.reader.pdf_reader import PDFReader
from agno.embedder.ollama import OllamaEmbedder
from agno.knowledge.pdf import PDFKnowledgeBase
from agno.models.ollama import Ollama
from agno.storage.sqlite import SqliteStorage
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb, SearchType
from rich import print
ollama_embedder = OllamaEmbedder(
    id="llama3.2-vision:11b",  # 使用与主模型相同的模型作为嵌入器
    host="http://*************:11434"
)


ollama_model = Ollama(
    id="qwen3:0.6b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)

agent_knowledge = PDFKnowledgeBase(
        path="agno-exp/pur.pdf",  # 修正PDF文件路径
        reader=PDFReader(chunk=True, chunk_size=1000),  # 恢复PDFReader配置
        vector_db=LanceDb(
            uri="tmp/lancedb_fixed",  # 使用新的数据库路径
            table_name="equipment_docs_v2",
            search_type=SearchType.vector,  # 使用简单的向量搜索
            embedder=ollama_embedder
        ),
    )
# Comment out after the knowledge base is loaded
# if agent_knowledge is not None:
#     agent_knowledge.load()

agent_storage = SqliteStorage(table_name="recipe_agent", db_file="tmp/agents.db")


def recipe_agent(user: str = "user"):
    session_id: Optional[str] = None

    # Ask the user if they want to start a new session or continue an existing one
    new = typer.confirm("Do you want to start a new session?")

    if not new:
        existing_sessions: List[str] = agent_storage.get_all_session_ids(user)
        if len(existing_sessions) > 0:
            session_id = existing_sessions[0]

    agent = Agent(
        user_id=user,
        session_id=session_id,
        model=ollama_model,
        instructions=dedent("""\
             您是一位装备管理领域的资深专家，对装备平台及装备条线的操作与管理有着深入的了解。您的专业知识主要来源于一份详尽的操作手册，该手册是您回答用户问题的权威依据。

    当用户提出问题时，请首先参考操作手册中的内容进行回答。从手册中提取与问题最直接相关的信息，并进行整合与提炼。若手册中信息不完整或用户问题涉及手册之外的领域，回答”抱歉，目前无法还无法回复您的提问“。在回答之前，请确保所提供信息的准确性。

    请使用专业术语，保持礼貌与耐心，为用户提供清晰、有条理的回答。回答应包含明确的引言、主体内容以及总结。

    示例回答框架：
    - 引言：“您好，关于您提出的关于装备平台/装备条线的问题，我根据操作手册为您提供以下回答：”
    - 主体内容：[详细描述操作步骤或管理流程，强调关键节点和注意事项，提供可能的解决方案或替代方案]
    - 总结：“希望以上回答能对您有所帮助。如有其他问题或需要更多信息，请随时告诉我。”\
        """),
        storage=agent_storage,
        knowledge=agent_knowledge,
        tools=[DuckDuckGoTools()],
        # Show tool calls in the response
        show_tool_calls=True,
        # To provide the agent with the chat history
        # We can either:
        # 1. Provide the agent with a tool to read the chat history
        # 2. Automatically add the chat history to the messages sent to the model
        #
        # 1. Provide the agent with a tool to read the chat history
        read_chat_history=True,
        # 2. Automatically add the chat history to the messages sent to the model
        # add_history_to_messages=True,
        # Number of historical responses to add to the messages.
        # num_history_responses=3,
        markdown=True,
    )

    print("You are about to chat with an agent!")
    if session_id is None:
        session_id = agent.session_id
        if session_id is not None:
            print(f"Started Session: {session_id}\n")
        else:
            print("Started Session\n")
    else:
        print(f"Continuing Session: {session_id}\n")

    # Runs the agent as a command line application
    agent.cli_app(markdown=True)


if __name__ == "__main__":
    typer.run(recipe_agent)
