"""
本地PDF文件知识库示例 - 使用Agno框架
展示如何从本地PDF文件创建知识库并进行问答
"""

from textwrap import dedent
import os
from pathlib import Path

from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.models.ollama import Ollama
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb, SearchType
from agno.embedder.ollama import OllamaEmbedder

def check_pdf_exists(pdf_path: str) -> bool:
    """检查PDF文件是否存在"""
    if not os.path.exists(pdf_path):
        print(f"❌ 错误: PDF文件不存在: {pdf_path}")
        print("请确保PDF文件路径正确")
        return False
    return True

def create_pdf_knowledge_agent(
    pdf_path: str,
    ollama_host: str = "http://*************:11434",
    model_name: str = "qwen3:0.6b",
    embedder_model: str = "llama3.2-vision:11b"
):
    """
    创建基于本地PDF的知识库Agent
    
    Args:
        pdf_path: PDF文件路径
        ollama_host: Ollama服务地址
        model_name: 主模型名称
        embedder_model: 嵌入模型名称
    
    Returns:
        配置好的Agent实例
    """
    
    # 检查PDF文件
    if not check_pdf_exists(pdf_path):
        return None
    
    print(f"📄 加载PDF文件: {pdf_path}")
    
    # 配置嵌入模型
    ollama_embedder = OllamaEmbedder(
        id=embedder_model,
        host=ollama_host
    )
    
    # 配置主模型
    ollama_model = Ollama(
        id=model_name,
        host=ollama_host,
    )
    
    # 创建知识库
    knowledge_base = PDFKnowledgeBase(
        path=pdf_path,  # 本地PDF文件路径
        reader=PDFReader(
            chunk=True,  # 启用分块读取
            chunk_size=1000,  # 分块大小
            chunk_overlap=100,  # 分块重叠
        ),
        vector_db=LanceDb(
            uri="tmp/lancedb_pdf",  # 向量数据库存储路径
            table_name="pdf_knowledge",
            search_type=SearchType.hybrid,  # 混合搜索
            embedder=ollama_embedder
        ),
    )
    
    # 创建Agent
    agent = Agent(
        model=ollama_model,
        knowledge=knowledge_base,
        instructions=dedent("""\
            你是一个专业的文档助手，能够基于提供的PDF文档内容回答用户问题。
            
            工作原则：
            1. 优先使用PDF文档中的信息回答问题
            2. 如果文档中没有相关信息，明确告知用户
            3. 回答要准确、详细，并引用文档中的具体内容
            4. 保持专业和友好的语调
            
            回答格式：
            - 首先说明信息来源（基于文档内容）
            - 提供详细的回答
            - 如果需要，可以引用文档中的具体段落
            - 最后询问是否需要更多信息
        """),
        tools=[DuckDuckGoTools()],  # 可选：添加搜索工具作为补充
        show_tool_calls=True,
        markdown=True,
        search_knowledge=True,  # 启用知识搜索
    )
    
    return agent

def load_knowledge_base(agent):
    """加载知识库"""
    print("🔄 正在加载知识库...")
    try:
        if agent.knowledge is not None:
            agent.knowledge.load(recreate=False)  # 设置为False避免重复创建
            print("✅ 知识库加载成功")
        else:
            print("❌ 知识库未配置")
    except Exception as e:
        print(f"❌ 知识库加载失败: {e}")
        return False
    return True

def test_pdf_qa(agent, questions):
    """测试PDF问答功能"""
    print("\n📝 开始PDF问答测试")
    print("=" * 50)
    
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. 问题: {question}")
        print("-" * 30)
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            print(f"❌ 回答失败: {e}")
        print()

def interactive_mode(agent):
    """交互模式"""
    print("\n🎮 进入交互模式 (输入 'quit' 退出)")
    print("=" * 50)
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
                
            print("AI助手:")
            agent.print_response(user_input, stream=True)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print("📚 本地PDF知识库示例")
    print("=" * 50)
    
    # 配置参数
    pdf_path = "./pur.pdf"  # 修改为你的PDF文件路径
    
    # 可以尝试多个可能的PDF路径
    possible_paths = [
        "./pur.pdf",
        "pur.pdf",
        "agno-exp/pur.pdf",
        "../pur.pdf"
    ]
    
    # 查找存在的PDF文件
    found_pdf = None
    for path in possible_paths:
        if os.path.exists(path):
            found_pdf = path
            break
    
    if not found_pdf:
        print("❌ 未找到PDF文件，请检查以下路径:")
        for path in possible_paths:
            print(f"  - {path}")
        print("\n请将PDF文件放在正确位置或修改代码中的路径")
        return
    
    # 创建Agent
    agent = create_pdf_knowledge_agent(found_pdf)
    if not agent:
        return
    
    # 加载知识库
    if not load_knowledge_base(agent):
        return
    
    # 测试问题
    test_questions = [
        "这个文档的主要内容是什么？",
        "文档中提到了哪些重要概念？",
        "请总结文档的核心要点",
    ]
    
    # 如果是装备管理文档，使用专门的问题
    if "pur" in found_pdf.lower() or "装备" in found_pdf:
        test_questions = [
            "如何访问装备系统?",
            "车型规则怎么设置?", 
            "车型组合测算怎么发布?",
            "装备平台的主要功能有哪些？"
        ]
    
    # 运行测试
    test_pdf_qa(agent, test_questions)
    
    # 可选：启动交互模式
    # interactive_mode(agent)

if __name__ == "__main__":
    main()
