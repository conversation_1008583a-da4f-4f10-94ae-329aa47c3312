"""
交互式财报HTML生成器
支持用户输入目录路径，兼容Windows目录结构
"""

import os
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from textwrap import dedent

from agno.agent import Agent
from agno.models.ollama import Ollama

class InteractiveFinancialGenerator:
    """交互式财报生成器"""
    
    def __init__(self):
        self.work_dir = None
        self.prompt_file = None
        self.agent = None
        
    def get_user_input(self) -> str:
        """获取用户输入的工作目录"""
        print("📁 财报HTML生成器 - 目录设置")
        print("=" * 50)
        
        while True:
            print("\n请选择工作目录设置方式：")
            print("1. 输入自定义目录路径")
            print("2. 使用当前目录下的默认文件夹")
            print("3. 退出程序")
            
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == "1":
                return self._get_custom_directory()
            elif choice == "2":
                return self._get_default_directory()
            elif choice == "3":
                print("👋 程序退出")
                exit(0)
            else:
                print("❌ 无效选择，请重新输入")
    
    def _get_custom_directory(self) -> str:
        """获取用户自定义目录"""
        print("\n📂 自定义目录设置")
        print("-" * 30)
        
        while True:
            print("\n请输入工作目录路径（Excel文件和生成的HTML将保存在此目录）：")
            print("示例：")
            print("  Windows: C:\\Users\\<USER>\\Documents\\财报数据")
            print("  或相对路径: .\\财报数据")
            
            user_input = input("\n目录路径: ").strip()
            
            if not user_input:
                print("❌ 路径不能为空")
                continue
            
            # 处理Windows路径
            work_dir = Path(user_input).resolve()
            
            # 询问是否创建目录
            if not work_dir.exists():
                print(f"\n📁 目录不存在: {work_dir}")
                create = input("是否创建此目录？(y/n): ").strip().lower()
                
                if create in ['y', 'yes', '是']:
                    try:
                        work_dir.mkdir(parents=True, exist_ok=True)
                        print(f"✅ 目录创建成功: {work_dir}")
                    except Exception as e:
                        print(f"❌ 目录创建失败: {e}")
                        continue
                else:
                    continue
            
            print(f"✅ 工作目录设置为: {work_dir}")
            return str(work_dir)

    def _get_default_directory(self) -> str:
        """使用默认目录"""
        current_dir = Path.cwd()
        work_dir = current_dir / "财报数据"
        
        print(f"\n📂 使用默认目录: {work_dir}")
        
        if not work_dir.exists():
            try:
                work_dir.mkdir(parents=True, exist_ok=True)
                print(f"✅ 默认目录创建成功")
            except Exception as e:
                print(f"❌ 默认目录创建失败: {e}")
                return self._get_custom_directory()
        
        return str(work_dir)
    
    def setup_directories(self, work_dir: str):
        """设置工作目录和相关文件"""
        self.work_dir = Path(work_dir)
        self.prompt_file = Path("./financial_prompt-n.txt")
        
        print(f"\n📋 目录结构:")
        print(f"  工作目录: {self.work_dir}")
        print(f"  Excel文件: 放在工作目录中")
        print(f"  HTML输出: 保存在工作目录中")
        print(f"  提示词文件: {self.prompt_file}")
        
        # 创建提示词文件（如果不存在）
        if not self.prompt_file.exists():
            self._create_prompt_file()
    
    def _create_prompt_file(self):
        """创建提示词文件"""
        prompt_content = dedent("""\
            # 角色：上市公司财报数据HTML页面生成专家

            ## 简介：
            - 语言：中文
            - 描述：专业的财报数据分析师和HTML动态网页设计专家

            ## 目标：
            生成一个完整的、可直接使用的HTML页面，用于展示上市公司财报数据

            ## 技术要求：
            1. 使用HTML5和CSS3
            2. 集成TailwindCSS进行样式设计
            3. 响应式设计，适配不同屏幕尺寸
            4. 添加适当的动画效果

            ## 设计规范：
            1. 根据公司特性选择适当的背景颜色和主题色调
            2. 应用大字体突出重点，创造视觉对比
            3. 中英文混排，大字体为主
            4. 使用简洁的卡片元素进行数据展示
            5. 突出显示关键财务指标
            6. 避免使用emoji作为主要图标

            ## 数据处理要求：
            - 重点关注资产负债表数据
            - 突出显示关键财务指标（总资产、总负债、净资产、营业收入、净利润等）
            - 计算并展示财务比率（资产负债率、流动比率等）
            - 提供同比增长率分析
            - 使用图表可视化数据

            ## 输出格式：
            请直接提供完整的HTML代码，包含所有必要的CSS，确保代码可以直接保存为.html文件并在浏览器中正常显示。

            ## 初始化：
            作为财报数据HTML页面生成专家，我已准备好为您创建完整的HTML页面。
        """)
        
        try:
            with open(self.prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt_content)
            print(f"✅ 已创建提示词文件: {self.prompt_file}")
        except Exception as e:
            print(f"❌ 提示词文件创建失败: {e}")
    
    def init_agent(self):
        """初始化AI Agent"""
        try:
            print("\n🤖 初始化AI模型...")
            
            # 读取提示词
            if self.prompt_file.exists():
                with open(self.prompt_file, 'r', encoding='utf-8') as f:
                    prompt = f.read()
            else:
                prompt = "你是一个专业的财报HTML生成专家。"
            
            # 配置模型
            model = Ollama(
                id="deepseek-r1:7b",
                host="http://*************:11434",
                options={
                    "temperature": 0.2,
                    "top_p": 0.9,
                    "num_ctx": 4096,
                }
            )
            
            # 创建Agent
            self.agent = Agent(
                model=model,
                instructions=prompt,
                markdown=False,
            )
            
            print("✅ AI模型初始化成功")
            
        except Exception as e:
            print(f"❌ AI模型初始化失败: {e}")
            raise
    
    def find_excel_files(self) -> List[Path]:
        """查找工作目录中的Excel文件"""
        excel_extensions = ['.xlsx', '.xls']
        excel_files = []
        
        for ext in excel_extensions:
            excel_files.extend(self.work_dir.glob(f"*{ext}"))
        
        return excel_files
    
    def read_excel_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """读取Excel文件"""
        try:
            print(f"📄 正在读取: {file_path.name}")
            
            # 读取Excel文件的所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None)
            
            file_info = {
                'filename': file_path.name,
                'filepath': str(file_path),
                'sheets': {}
            }
            
            # 处理每个工作表
            for sheet_name, df in excel_data.items():
                # 转换为字符串格式，便于模型处理
                sheet_data = df.to_string(index=False, max_rows=50)  # 限制行数
                file_info['sheets'][sheet_name] = {
                    'data': sheet_data,
                    'shape': df.shape,
                    'columns': df.columns.tolist()
                }
            
            print(f"✅ 成功读取: {file_path.name} ({len(excel_data)} 个工作表)")
            return file_info
            
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path.name}: {e}")
            return None
    
    def generate_html(self, excel_data: Dict[str, Any]) -> Optional[str]:
        """生成HTML报告"""
        try:
            # 构建发送给模型的数据
            data_summary = f"文件名: {excel_data['filename']}\n\n"
            
            for sheet_name, sheet_info in excel_data['sheets'].items():
                data_summary += f"工作表: {sheet_name}\n"
                data_summary += f"数据维度: {sheet_info['shape']}\n"
                data_summary += f"列名: {', '.join(sheet_info['columns'])}\n"
                data_summary += f"数据内容:\n{sheet_info['data'][:1500]}...\n\n"  # 限制长度
            
            # 发送给模型
            prompt = f"""
            请分析以下财报数据并生成完整的HTML页面：

            {data_summary}

            要求：
            1. 生成一个完整的HTML页面，包含所有必要的CSS样式
            2. 使用现代化的设计风格
            3. 突出显示关键财务数据
            4. 确保页面可以直接在浏览器中打开
            5. 添加适当的颜色和布局

            请直接输出完整的HTML代码：
            """
            
            print(f"🤖 正在生成HTML报告...")
            response = self.agent.run(prompt)
            
            return response.content
            
        except Exception as e:
            print(f"❌ HTML生成失败: {e}")
            return None
    
    def save_html(self, html_content: str, original_filename: str) -> Optional[str]:
        """保存HTML文件"""
        try:
            # 生成输出文件名
            base_name = Path(original_filename).stem
            output_file = self.work_dir / f"{base_name}_财报.html"
            
            # 保存HTML内容
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML报告已保存: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 保存HTML失败: {e}")
            return None
    
    def process_files(self):
        """处理所有Excel文件"""
        print(f"\n📊 扫描Excel文件...")
        excel_files = self.find_excel_files()
        
        if not excel_files:
            print(f"❌ 在目录 {self.work_dir} 中未找到Excel文件")
            print("请将Excel文件放入工作目录后重新运行程序")
            return
        
        print(f"📋 找到 {len(excel_files)} 个Excel文件:")
        for i, file_path in enumerate(excel_files, 1):
            print(f"  {i}. {file_path.name}")
        
        # 处理每个文件
        success_count = 0
        for i, file_path in enumerate(excel_files, 1):
            print(f"\n📈 处理文件 {i}/{len(excel_files)}: {file_path.name}")
            print("-" * 50)
            
            # 读取Excel文件
            excel_data = self.read_excel_file(file_path)
            if not excel_data:
                continue
            
            # 生成HTML
            html_content = self.generate_html(excel_data)
            if not html_content:
                continue
            
            # 保存HTML文件
            output_file = self.save_html(html_content, file_path.name)
            if output_file:
                success_count += 1
                print(f"🎉 成功生成: {Path(output_file).name}")
        
        print(f"\n✅ 处理完成！")
        print(f"📊 成功处理: {success_count}/{len(excel_files)} 个文件")
        print(f"📁 输出目录: {self.work_dir}")
    
    def run(self):
        """运行主程序"""
        try:
            print("🏢 上市公司财报数据HTML页面生成系统")
            print("=" * 60)
            
            # 1. 获取工作目录
            work_dir = self.get_user_input()
            
            # 2. 设置目录结构
            self.setup_directories(work_dir)
            
            # 3. 初始化AI模型
            self.init_agent()
            
            # 4. 处理Excel文件
            self.process_files()
            
            print(f"\n🎊 程序执行完成！")
            print(f"📂 请查看工作目录中生成的HTML文件: {self.work_dir}")
            
        except KeyboardInterrupt:
            print(f"\n\n👋 用户中断程序")
        except Exception as e:
            print(f"\n❌ 程序执行失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    generator = InteractiveFinancialGenerator()
    generator.run()

if __name__ == "__main__":
    main()
