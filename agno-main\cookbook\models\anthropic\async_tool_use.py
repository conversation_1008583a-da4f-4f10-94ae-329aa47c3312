"""
Async example using <PERSON> with tool calls.
"""

import asyncio

from agno.agent import Agent
from agno.models.anthropic import <PERSON>
from agno.tools.duckduckgo import DuckDuckGoTools

agent = Agent(
    model=<PERSON>(id="claude-sonnet-4-20250514"),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

asyncio.run(agent.aprint_response("Whats happening in France?", stream=True))
