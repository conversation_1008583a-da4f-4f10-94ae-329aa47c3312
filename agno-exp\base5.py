from textwrap import dedent

from agno.models.ollama import Ollama
from agno.agent import Agent
from agno.team import Team
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.yfinance import YFinanceTools

ollama_web = Ollama(
    id="qwen3:0.6b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)

ollama_finance = Ollama(
    id="qwen3:0.6b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)

ollama_team = Ollama(
    id="qwen3:0.6b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)


web_agent = Agent(
    name="Web Agent",
    role="检索网络信息",
    model=ollama_web,
    tools=[DuckDuckGoTools()],
    instructions=dedent("""\
        您是一位经验丰富的网络研究员和新闻分析师！
        信息检索需遵循以下步骤：            
            1.优先选择最新且相关性强的信息来源
            2.对多源信息进行交叉验证
            3.侧重权威媒体和官方信源
            4.始终标注信息来源链接
            5.聚焦市场动态新闻与重大进展
        风格指南要求：
            -采用清晰、专业的新闻写作风格
            -使用项目符号列出关键要点
            -包含相关直接引语（如可获得）
            -注明每条新闻的具体日期和时间
            -突出市场情绪与行业趋势
            -结尾需包含整体叙事分析
            -特别关注监管动态、财报披露和战略公告 \
    """),
    show_tool_calls=True,
    markdown=True,
)

finance_agent = Agent(
    name="Finance Agent",
    role="提取金融数据",
    model=ollama_finance,
    tools=[
        YFinanceTools(stock_price=True, analyst_recommendations=True, company_info=True)
    ],
    instructions=dedent("""\
        您是一名精通市场数据的资深金融分析师! 📊

        分析财务数据时请遵循以下步骤：‌
        1.首先获取‌最新股价、成交量及日振幅
        2.详细呈现‌分析师建议与一致目标价
        3.纳入关键指标‌：市盈率(P/E)、市值、52周波动区间
        4.解析‌交易形态与量能趋势
        5.对比‌相关行业指数表现
        ‌格式规范：‌
          -‌表格化呈现‌结构化数据
          -‌明确标注‌各数据区块标题
          -术语注释‌：专业词汇附加简明解释
          -‌异动标注‌：用📈📉符号突出显著变化
          -‌要点凝练‌：核心洞见分点列示
          -‌历史参照‌：当前值与长期均值对比
          -收尾要求‌：基于数据推演财务前景\
    """),
    show_tool_calls=True,
    markdown=True,
)

agent_team = Team(
    members=[web_agent, finance_agent],
    model=ollama_team,
    mode="coordinate",
    success_criteria=dedent("""\
        一份全面的财经新闻报道，包含清晰的板块划分和数据驱动的深度分析.
    """),
    instructions=dedent("""\
        您作为著名财经新闻编辑台的主编! 📰
        ‌您的职责：‌
            协调网络研究员与金融分析师的工作
            将各方发现整合为引人入胜的叙事
            确保所有信息来源可靠且经过核验
            均衡呈现新闻事件与数据洞察
            突出核心风险与机遇
        ‌风格指南：‌
            -‌吸睛标题‌开篇
            -以‌强有力的执行摘要‌起笔
            -优先展示财务数据‌，随后补充新闻背景
            -不同信息类型间使用‌清晰板块分隔‌
            -附带‌相关图表/表格‌（如有）
            -增设"‌市场情绪‌"板块（实时氛围）
            -文末设置"‌核心要点‌"总结栏
            -适时以"‌风险因素‌"收尾
            -落款标注"‌市场观察团队‌"及‌当前日期\
    """),
    add_datetime_to_instructions=True,
    show_tool_calls=True,
    markdown=True,
    enable_agentic_context=True,
    show_members_responses=False,
)

agent_team.print_response(
    message="总结分析师建议，分享NVDA的最新消息",
    stream=True,
)
agent_team.print_response(
    message="AI半导体公司的市场前景和财务业绩如何？",
    stream=True,
)
agent_team.print_response(
    message="分析TSLA的最新发展和财务业绩",
    stream=True,
)