"""
高级示例 - 结构化输出和复杂工具
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from textwrap import dedent
from typing import List
from pydantic import BaseModel, Field
import json

# 配置Ollama模型
ollama_model = Ollama(
    id="qwen3:0.6b",
    host="http://*************:11434",
    options={
        "temperature": 0.3,
        "top_p": 0.9,
    }
)

# 定义结构化输出模型
class TaskPlan(BaseModel):
    """任务计划的结构化输出"""
    title: str = Field(description="任务标题")
    description: str = Field(description="任务描述")
    steps: List[str] = Field(description="执行步骤列表")
    estimated_time: str = Field(description="预估完成时间")
    priority: str = Field(description="优先级：高/中/低")
    tags: List[str] = Field(description="相关标签")

class WeatherInfo(BaseModel):
    """天气信息的结构化输出"""
    location: str = Field(description="地点")
    temperature: str = Field(description="温度")
    condition: str = Field(description="天气状况")
    humidity: str = Field(description="湿度")
    recommendation: str = Field(description="建议")

# 高级工具函数
def analyze_text(text: str) -> str:
    """
    分析文本内容
    
    Args:
        text: 要分析的文本
    
    Returns:
        分析结果
    """
    if not text.strip():
        return "错误：文本不能为空"
    
    word_count = len(text.split())
    char_count = len(text)
    sentence_count = len([s for s in text.split('.') if s.strip()])
    
    analysis = {
        "字符数": char_count,
        "单词数": word_count,
        "句子数": sentence_count,
        "平均句长": round(word_count / max(sentence_count, 1), 2),
        "文本类型": "长文本" if word_count > 100 else "短文本"
    }
    
    return f"文本分析结果: {json.dumps(analysis, ensure_ascii=False, indent=2)}"

def generate_ideas(topic: str, count: int = 5) -> str:
    """
    生成创意想法
    
    Args:
        topic: 主题
        count: 生成数量
    
    Returns:
        创意列表
    """
    ideas_templates = [
        f"基于{topic}的创新应用",
        f"{topic}与其他领域的结合",
        f"改进现有{topic}解决方案",
        f"{topic}的未来发展趋势",
        f"{topic}的社会影响分析",
        f"个人如何学习{topic}",
        f"{topic}的商业机会",
        f"{topic}相关的技术挑战"
    ]
    
    import random
    selected_ideas = random.sample(ideas_templates, min(count, len(ideas_templates)))
    
    result = f"关于'{topic}'的{count}个创意想法:\n"
    for i, idea in enumerate(selected_ideas, 1):
        result += f"{i}. {idea}\n"
    
    return result

def create_summary(content: str, max_length: int = 100) -> str:
    """
    创建内容摘要
    
    Args:
        content: 原始内容
        max_length: 最大长度
    
    Returns:
        摘要内容
    """
    if len(content) <= max_length:
        return f"摘要: {content}"
    
    # 简单的摘要逻辑：取前面部分
    summary = content[:max_length-3] + "..."
    return f"摘要: {summary}"

# 创建高级Agent
def create_structured_agent():
    """创建支持结构化输出的Agent"""
    return Agent(
        model=ollama_model,
        tools=[analyze_text, generate_ideas, create_summary],
        show_tool_calls=True,
        instructions=dedent("""\
            你是一个高级AI助手，具备以下能力：
            
            1. 结构化输出：能够按照指定格式输出结构化数据
            2. 文本分析：可以分析文本的各种特征
            3. 创意生成：能够针对特定主题生成创意想法
            4. 内容摘要：可以为长文本创建简洁摘要
            
            工作原则：
            - 根据用户需求选择合适的工具
            - 提供准确、有用的信息
            - 保持专业和友好的态度
            - 用中文回答问题
        """),
        markdown=True,
    )

def test_structured_output():
    """测试结构化输出"""
    print("📋 结构化输出测试")
    print("-" * 30)
    
    agent = create_structured_agent()
    
    try:
        # 设置响应模型为任务计划
        agent.response_model = TaskPlan
        
        response = agent.run("帮我制定一个学习Python编程的计划")
        
        if hasattr(response, 'content') and isinstance(response.content, TaskPlan):
            plan = response.content
            print("📋 生成的任务计划:")
            print(f"标题: {plan.title}")
            print(f"描述: {plan.description}")
            print(f"优先级: {plan.priority}")
            print(f"预估时间: {plan.estimated_time}")
            print(f"标签: {', '.join(plan.tags)}")
            print("执行步骤:")
            for i, step in enumerate(plan.steps, 1):
                print(f"  {i}. {step}")
        else:
            print("普通回复:")
            print(response.content)
            
    except Exception as e:
        print(f"❌ 结构化输出失败: {e}")

def test_advanced_tools():
    """测试高级工具"""
    print("\n🔧 高级工具测试")
    print("-" * 30)
    
    agent = create_structured_agent()
    
    test_queries = [
        "分析这段文本：人工智能正在改变我们的世界。它不仅影响着科技行业，也在医疗、教育、交通等各个领域发挥着重要作用。",
        "为'区块链技术'生成3个创意想法",
        "为以下内容创建摘要：机器学习是人工智能的一个重要分支，它使计算机能够从数据中学习而无需明确编程。通过算法和统计模型，机器学习系统可以识别模式、做出预测并改进性能。"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. 用户: {query}")
        print("-" * 40)
        try:
            agent.print_response(query, stream=True)
        except Exception as e:
            print(f"❌ 错误: {e}")
        print()

# 主执行逻辑
if __name__ == "__main__":
    print("🚀 高级Agno示例")
    print("=" * 50)
    
    try:
        # 测试结构化输出
        test_structured_output()
        
        # 测试高级工具
        test_advanced_tools()
        
        print("\n✅ 高级功能测试完成")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
