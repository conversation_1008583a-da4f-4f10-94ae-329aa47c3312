# Agno框架示例 - 使用Ollama和qwen3:0.6b

这个目录包含了使用Agno框架与Ollama服务器和qwen3:0.6b模型的示例代码。

## 环境配置

### 1. 确保Ollama服务运行

确保Ollama服务在 `http://*************:11434` 上运行，并且已经拉取了qwen3:0.6b模型：

```bash
# 拉取模型（如果还没有的话）
ollama pull qwen3:0.6b

# 验证模型是否可用
ollama list
```

### 2. 安装依赖

```bash
# 安装Agno和Ollama客户端
pip install agno ollama

# 或者如果有requirements.txt
pip install -r requirements.txt
```

## 示例文件说明

### 1. `base1.py` - 基础示例

最简单的Agno + Ollama示例，展示：
- 如何配置Ollama模型
- 基本的对话功能
- 中英文对话测试

```bash
python agno-exp/base1.py
```

### 2. `simple_tools_example.py` - 工具使用示例

展示如何创建和使用自定义工具：
- 天气查询（模拟）
- 抛硬币
- 掷骰子
- 密码生成
- 文本统计
- 时间查询

```bash
python agno-exp/simple_tools_example.py
```

### 3. `advanced_example.py` - 高级功能示例

展示Agno框架的高级功能：
- 结构化输出（Pydantic模型）
- 多种Agent配置
- 工具集成
- 会话管理

```bash
python agno-exp/advanced_example.py
```

### 4. PDF知识库示例

#### 4.1 故障排除工具
```bash
python agno-exp/troubleshoot_pdf.py
```

#### 4.2 Ollama连接测试
```bash
python agno-exp/test_ollama_connection.py
```

#### 4.3 调试工具
```bash
python agno-exp/debug_pdf.py
```

#### 4.4 工作的PDF示例（推荐）
```bash
python agno-exp/working_pdf_example.py
```

#### 4.5 完整PDF知识库（修复版）
```bash
python agno-exp/base3Pdf.py
```

#### 4.6 简化PDF示例
```bash
python agno-exp/simple_pdf_example.py
```

## 核心概念

### Ollama模型配置

```python
from agno.models.ollama import Ollama

# 基本配置
model = Ollama(
    id="qwen3:0.6b",  # 模型名称
    host="http://*************:11434",  # Ollama服务地址
)

# 高级配置
model = Ollama(
    id="qwen3:0.6b",
    host="http://*************:11434",
    options={
        "temperature": 0.7,  # 控制创造性
        "top_p": 0.9,       # 控制多样性
    }
)
```

### Agent创建

```python
from agno.agent import Agent

# 基础Agent
agent = Agent(
    model=model,
    markdown=True,
    instructions="你的系统提示词"
)

# 带工具的Agent
agent = Agent(
    model=model,
    tools=[tool1, tool2],  # 自定义工具列表
    show_tool_calls=True,  # 显示工具调用
    markdown=True,
)
```

### 自定义工具

```python
def my_tool(param: str) -> str:
    """
    工具描述
    
    Args:
        param: 参数描述
    
    Returns:
        返回值描述
    """
    # 工具逻辑
    return "结果"
```

### 结构化输出

```python
from pydantic import BaseModel, Field

class MyResponse(BaseModel):
    title: str = Field(description="标题")
    content: str = Field(description="内容")

# 设置响应模型
agent.response_model = MyResponse
response = agent.run("用户输入")
```

## 使用技巧

### 1. 调试模式

```python
agent = Agent(
    model=model,
    debug_mode=True,  # 启用调试模式
)
```

### 2. 流式输出

```python
# 流式打印响应
agent.print_response("用户输入", stream=True)

# 获取流式响应
for chunk in agent.run_stream("用户输入"):
    print(chunk.content, end="", flush=True)
```

### 3. 错误处理

```python
try:
    response = agent.run("用户输入")
    print(response.content)
except Exception as e:
    print(f"错误: {e}")
```

## PDF知识库详细说明

### 问题解决方案

我们成功解决了PDF知识库的以下问题：

1. **修复了PDFReader参数错误**：
   - 移除了不支持的 `chunk_overlap` 参数
   - 使用正确的 `PDFReader(chunk=True, chunk_size=1000)` 配置

2. **解决了LanceDB兼容性问题**：
   - 提供了多种实现方案
   - 包含了不使用向量数据库的简化版本

3. **优化了模型配置**：
   - 使用相同模型作为主模型和嵌入器避免兼容性问题
   - 配置了合适的超时和重试机制

### 推荐使用方案

1. **working_pdf_example.py** - 最稳定的方案，直接将PDF内容嵌入Agent指令
2. **base3Pdf.py** - 完整的向量数据库方案（已修复）
3. **troubleshoot_pdf.py** - 诊断工具，帮助排查问题

## 常见问题

### 1. 连接失败

如果出现连接错误，请检查：
- Ollama服务是否运行
- 地址和端口是否正确
- 网络连接是否正常

### 2. 模型未找到

如果提示模型未找到：
```bash
ollama pull qwen3:0.6b
```

### 3. 工具调用失败

确保工具函数：
- 有正确的类型注解
- 有详细的文档字符串
- 返回字符串类型

## 扩展示例

你可以基于这些示例创建更复杂的应用：

1. **聊天机器人**: 添加会话历史和上下文管理
2. **任务助手**: 集成更多实用工具
3. **知识问答**: 连接外部知识库
4. **代码助手**: 添加代码生成和分析工具

## 参考资源

- [Agno官方文档](https://docs.agno.ai/)
- [Ollama官方文档](https://ollama.ai/)
- [Pydantic文档](https://docs.pydantic.dev/)

## 许可证

本示例代码遵循MIT许可证。
