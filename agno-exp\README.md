# 上市公司财报数据HTML页面生成系统

基于Agno框架和Ollama模型的智能财报HTML生成工具，能够读取多个Excel财报文件并生成综合分析的HTML页面。

## 🚀 核心功能

- **多文件综合分析**: 遍历指定目录下的所有Excel文件作为数据源
- **生成单一HTML报告**: 将所有数据整合到一个综合分析页面
- **AI智能分析**: 使用大模型进行财务数据分析和对比
- **现代化设计**: 生成包含TailwindCSS样式的响应式页面
- **用户友好界面**: 支持Windows目录结构，交互式目录选择

## 📁 项目结构

```
agno-exp/
├── start_financial_generator.py         # 🎯 主启动脚本
├── interactive_financial_generator.py   # 💼 核心生成器
├── create_sample_data.py               # 📊 示例数据创建工具
├── financial_prompt.txt                # 📝 AI提示词文件
├── financial_data/                     # 📂 Excel输入目录
├── html_reports/                       # 📂 HTML输出目录
├── requirements.txt                     # 📋 依赖列表
├── FINANCIAL_README.md                 # 📖 详细使用说明
├── 使用指南.md                         # 📖 中文使用指南
└── 项目总结.md                         # 📋 项目总结
```

## 🎯 使用方法

### 一键启动
```bash
python agno-exp/start_financial_generator.py
```

### 功能选项
1. **财报HTML生成器** - 综合分析多个Excel文件
2. **创建示例数据** - 生成测试用Excel文件
3. **帮助说明** - 查看详细使用指南

## 📊 工作流程

1. **准备数据** - 将Excel财报文件放入指定目录
2. **选择目录** - 程序启动时选择工作目录
3. **自动扫描** - 系统自动扫描所有Excel文件
4. **综合分析** - AI分析所有数据源并生成对比
5. **输出HTML** - 生成包含综合分析的HTML页面

## 🔧 技术特性

- **Agno框架**: AI Agent管理和模型调用
- **Ollama集成**: 本地大语言模型服务
- **Excel处理**: 支持.xlsx和.xls格式
- **Windows兼容**: 完美支持Windows路径结构
- **响应式设计**: 生成的HTML适配各种设备

## 📋 Excel文件要求

### 支持格式
- ✅ `.xlsx` (推荐)
- ✅ `.xls`

### 数据结构示例
```
项目                    2023年12月31日    2022年12月31日
流动资产                              
货币资金                1,250,000,000     1,100,000,000
应收账款                2,100,000,000     1,850,000,000
存货                      450,000,000       380,000,000
...
```

## 🎨 生成的HTML特性

- **综合对比分析**: 多个数据源的横向对比
- **关键指标突出**: 总资产、净利润等核心数据
- **现代化设计**: TailwindCSS样式和响应式布局
- **数据可视化**: 表格和图表展示财务数据
- **专业展示**: 适合财务分析和报告展示

## 🛠️ 环境要求

- Python 3.7+
- Agno框架
- Ollama服务 (运行在 http://10.122.17.186:11434)
- qwen3:0.6b模型

## 📖 详细文档

- [详细使用说明](FINANCIAL_README.md)
- [中文使用指南](使用指南.md)
- [项目总结](项目总结.md)

## 🎉 主要优势

1. **一站式解决方案**: 从Excel读取到HTML生成的完整流程
2. **智能综合分析**: AI驱动的多数据源对比分析
3. **用户友好**: 交互式界面和详细进度提示
4. **专业输出**: 生成可直接用于展示的HTML页面
5. **高度可定制**: 可修改提示词调整分析风格

这个系统为上市公司财报数据的综合分析和可视化展示提供了强大而灵活的解决方案。
