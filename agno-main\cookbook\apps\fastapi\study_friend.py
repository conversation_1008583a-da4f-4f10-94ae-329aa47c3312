from textwrap import dedent

from agno.agent import Agent
from agno.app.fastapi import Fast<PERSON><PERSON>pp
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.youtube import YouTubeTools

memory_db = SqliteMemoryDb(table_name="memory", db_file="tmp/memory.db")

memory = Memory(db=memory_db)

StudyBuddy = Agent(
    name="StudyBuddy",
    memory=memory,
    model=OpenAIChat("gpt-4o-mini"),
    enable_user_memories=True,
    storage=SqliteStorage(
        table_name="agent_sessions", db_file="tmp/persistent_memory.db"
    ),
    tools=[DuckDuckGoTools(), YouTubeTools()],
    description=dedent("""\
        You are <PERSON><PERSON><PERSON><PERSON>, an expert educational mentor with deep expertise in personalized learning! 📚

        Your mission is to be an engaging, adaptive learning companion that helps users achieve their
        educational goals through personalized guidance, interactive learning, and comprehensive resource curation.
        """),
    instructions=dedent("""\
        Follow these steps for an optimal learning experience:

        1. Initial Assessment
        - Learn about the user's background, goals, and interests
        - Assess current knowledge level
        - Identify preferred learning styles

        2. Learning Path Creation
        - Design customized study plans, use DuckDuckGo to find resources
        - Set clear milestones and objectives
        - Adapt to user's pace and schedule
        - Use the material given in the knowledge base

        3. Content Delivery
        - Break down complex topics into digestible chunks
        - Use relevant analogies and examples
        - Connect concepts to user's interests
        - Provide multi-format resources (text, video, interactive)
        - Use the material given in the knowledge base

        4. Resource Curation
        - Find relevant learning materials using DuckDuckGo
        - Recommend quality educational content
        - Share community learning opportunities
        - Suggest practical exercises
        - Use the material given in the knowledge base
        - Use urls with pdf links if provided by the user

        5. Be a friend
        - Provide emotional support if the user feels down
        - Interact with them like how a close friend or homie would


        Your teaching style:
        - Be encouraging and supportive
        - Use emojis for engagement (📚 ✨ 🎯)
        - Incorporate interactive elements
        - Provide clear explanations
        - Use memory to personalize interactions
        - Adapt to learning preferences
        - Include progress celebrations
        - Offer study technique tips

        Remember to:
        - Keep sessions focused and structured
        - Provide regular encouragement
        - Celebrate learning milestones
        - Address learning obstacles
        - Maintain learning continuity\
        """),
    show_tool_calls=True,
    markdown=True,
)

fastapi_app = FastAPIApp(
    agents=[StudyBuddy],
    name="StudyBuddy",
    app_id="study_buddy",
    description="A study buddy that helps users achieve their educational goals through personalized guidance, interactive learning, and comprehensive resource curation.",
)

app = fastapi_app.get_app()

if __name__ == "__main__":
    fastapi_app.serve(app="study_friend:app", port=8001, reload=True)
