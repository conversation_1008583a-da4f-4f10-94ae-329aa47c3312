"""
基础Agno示例1 - 带工具的Agent
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from textwrap import dedent
from datetime import datetime

# 配置Ollama模型
ollama_model = Ollama(
    id="qwen3:0.6b",
    host="http://*************:11434",
)

# 定义自定义工具
def get_current_time() -> str:
    """获取当前时间"""
    return f"当前时间是: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

def calculate_math(expression: str) -> str:
    """
    计算数学表达式
    
    Args:
        expression: 数学表达式，如 "2+3*4"
    
    Returns:
        计算结果
    """
    try:
        # 简单的安全检查
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars or c.isspace() for c in expression):
            return "错误：表达式包含不允许的字符"
        
        result = eval(expression)
        return f"计算结果: {expression} = {result}"
    except Exception as e:
        return f"计算错误: {str(e)}"

def get_weather(city: str = "北京") -> str:
    """
    获取天气信息（模拟）
    
    Args:
        city: 城市名称
    
    Returns:
        天气信息
    """
    import random
    weather_conditions = ["晴天", "多云", "小雨", "阴天"]
    temperature = random.randint(-5, 35)
    condition = random.choice(weather_conditions)
    
    return f"{city}的天气: {condition}, 温度{temperature}°C"

# 创建带工具的Agent
agent = Agent(
    model=ollama_model,
    tools=[get_current_time, calculate_math, get_weather],
    show_tool_calls=True,
    instructions=dedent("""\
        你是一个功能强大的AI助手，可以使用多种工具：
        
        1. get_current_time: 获取当前时间
        2. calculate_math: 进行数学计算
        3. get_weather: 查询天气信息
        
        当用户需要这些功能时，请主动使用相应的工具。
        请用中文回答问题，保持友好和专业的态度。
    """),
    markdown=True,
)

# 测试工具使用
if __name__ == "__main__":
    print("🔧 带工具的Agno示例")
    print("=" * 40)
    
    test_queries = [
        "现在几点了？",
        "帮我计算 15 * 8 + 25",
        "查询一下上海的天气",
        "北京今天天气怎么样？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. 用户: {query}")
        print("-" * 30)
        try:
            agent.print_response(query, stream=True)
        except Exception as e:
            print(f"❌ 错误: {e}")
        print()
