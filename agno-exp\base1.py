from agno.agent import Agent
from agno.models.ollama import Ollama
from textwrap import dedent

# 正确配置Ollama实例
ollama_model = Ollama(
    id="gemma3n:e2b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)

# 创建Agent实例
agent = Agent(
    model=ollama_model,
    markdown=True,
    instructions=dedent("""\
        你是一个友好的AI助手，能够用中文和英文回答问题。
        请用简洁明了的方式回答用户的问题，并保持礼貌和专业。
        思考过程不需要输出。
    """),
)

# 测试基本对话
print("=== 基本对话测试 ===")
try:
    agent.print_response("你好，请介绍一下你自己。", stream=True)
    print("\n")
except Exception as e:
    print(f"对话失败: {e}")

# 测试英文对话
print("=== 英文对话测试 ===")
try:
    agent.print_response("What is the capital of China?", stream=True)
    print("\n")
except Exception as e:
    print(f"英文对话失败: {e}")

# 测试数学问题
print("=== 数学问题测试 ===")
try:
    agent.print_response("计算 15 + 27 = ?", stream=True)
    print("\n")
except Exception as e:
    print(f"数学问题失败: {e}")