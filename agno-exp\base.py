"""
基础Agno示例 - 使用Ollama模型
"""

from agno.agent import Agent
from agno.models.ollama import Ollama

# 配置Ollama模型
ollama_model = Ollama(
    id="qwen3:0.6b",
    host="http://*************:11434",
)

# 创建Agent
agent = Agent(
    model=ollama_model,
    instructions="你是一个友好的AI助手，请用中文回答问题。",
    markdown=True,
)

# 测试对话
if __name__ == "__main__":
    print("🤖 基础Agno示例")
    print("=" * 30)
    
    try:
        # 简单对话测试
        agent.print_response("你好，请介绍一下你自己。", stream=True)
        print("\n")
        
        # 问答测试
        agent.print_response("什么是人工智能？", stream=True)
        print("\n")
        
        # 计算测试
        agent.print_response("请计算 25 + 17 = ?", stream=True)
        print("\n")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
