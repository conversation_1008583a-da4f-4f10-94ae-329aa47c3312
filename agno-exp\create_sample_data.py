"""
创建示例财报数据
"""

import pandas as pd
import os
from pathlib import Path

def create_sample_financial_data():
    """创建示例财报数据"""
    
    # 创建目录
    data_dir = Path("agno-exp/financial_data")
    data_dir.mkdir(exist_ok=True)
    
    # 示例公司1：科技公司资产负债表
    tech_company_data = {
        '项目': [
            '流动资产',
            '货币资金',
            '交易性金融资产',
            '应收账款',
            '预付款项',
            '其他应收款',
            '存货',
            '其他流动资产',
            '流动资产合计',
            '',
            '非流动资产',
            '长期股权投资',
            '固定资产',
            '在建工程',
            '无形资产',
            '商誉',
            '递延所得税资产',
            '其他非流动资产',
            '非流动资产合计',
            '',
            '资产总计',
            '',
            '流动负债',
            '短期借款',
            '应付账款',
            '预收款项',
            '应付职工薪酬',
            '应交税费',
            '其他应付款',
            '一年内到期的非流动负债',
            '流动负债合计',
            '',
            '非流动负债',
            '长期借款',
            '应付债券',
            '递延所得税负债',
            '其他非流动负债',
            '非流动负债合计',
            '',
            '负债合计',
            '',
            '所有者权益',
            '股本',
            '资本公积',
            '盈余公积',
            '未分配利润',
            '所有者权益合计',
            '',
            '负债和所有者权益总计'
        ],
        '2023年12月31日': [
            '',
            1250000000,
            850000000,
            2*********,
            180000000,
            320000000,
            450000000,
            280000000,
            5430000000,
            '',
            '',
            1800000000,
            3200000000,
            650000000,
            1*********,
            2800000000,
            *********,
            420000000,
            10*********,
            '',
            15550000000,
            '',
            '',
            800000000,
            *********0,
            350000000,
            280000000,
            180000000,
            420000000,
            *********,
            3380000000,
            '',
            '',
            *********0,
            2000000000,
            *********,
            180000000,
            3500000000,
            '',
            6880000000,
            '',
            '',
            *********0,
            4200000000,
            850000000,
            2620000000,
            8670000000,
            '',
            15550000000
        ],
        '2022年12月31日': [
            '',
            1*********,
            720000000,
            1850000000,
            160000000,
            280000000,
            380000000,
            250000000,
            4740000000,
            '',
            '',
            1650000000,
            2900000000,
            580000000,
            950000000,
            2600000000,
            130000000,
            380000000,
            9190000000,
            '',
            13930000000,
            '',
            '',
            650000000,
            1050000000,
            300000000,
            250000000,
            *********,
            380000000,
            *********,
            2900000000,
            '',
            '',
            *********0,
            1800000000,
            *********,
            *********,
            3050000000,
            '',
            5950000000,
            '',
            '',
            *********0,
            3800000000,
            *********,
            2430000000,
            7980000000,
            '',
            13930000000
        ]
    }
    
    # 创建DataFrame并保存
    tech_df = pd.DataFrame(tech_company_data)
    tech_file = data_dir / "科技公司_资产负债表_2023.xlsx"
    tech_df.to_excel(tech_file, index=False, sheet_name="资产负债表")
    print(f"✅ 已创建示例文件: {tech_file}")
    
    # 示例公司2：制造业公司资产负债表
    manufacturing_data = {
        '项目': [
            '流动资产',
            '货币资金',
            '应收账款',
            '预付款项',
            '存货',
            '其他流动资产',
            '流动资产合计',
            '',
            '非流动资产',
            '固定资产',
            '在建工程',
            '无形资产',
            '长期股权投资',
            '递延所得税资产',
            '非流动资产合计',
            '',
            '资产总计',
            '',
            '流动负债',
            '短期借款',
            '应付账款',
            '预收款项',
            '应付职工薪酬',
            '应交税费',
            '其他应付款',
            '流动负债合计',
            '',
            '非流动负债',
            '长期借款',
            '递延所得税负债',
            '非流动负债合计',
            '',
            '负债合计',
            '',
            '所有者权益',
            '股本',
            '资本公积',
            '盈余公积',
            '未分配利润',
            '所有者权益合计',
            '',
            '负债和所有者权益总计'
        ],
        '2023年12月31日': [
            '',
            800000000,
            *********0,
            *********,
            2200000000,
            180000000,
            4800000000,
            '',
            '',
            5500000000,
            800000000,
            650000000,
            300000000,
            80000000,
            7330000000,
            '',
            12130000000,
            '',
            '',
            *********0,
            1800000000,
            250000000,
            180000000,
            *********,
            280000000,
            3830000000,
            '',
            '',
            2200000000,
            *********,
            2350000000,
            '',
            6180000000,
            '',
            '',
            800000000,
            2*********,
            450000000,
            2600000000,
            5950000000,
            '',
            12130000000
        ],
        '2022年12月31日': [
            '',
            720000000,
            1350000000,
            *********,
            1950000000,
            *********,
            4270000000,
            '',
            '',
            5200000000,
            650000000,
            580000000,
            280000000,
            70000000,
            6780000000,
            '',
            11050000000,
            '',
            '',
            *********0,
            1600000000,
            220000000,
            160000000,
            *********,
            250000000,
            3330000000,
            '',
            '',
            2000000000,
            130000000,
            2130000000,
            '',
            5460000000,
            '',
            '',
            800000000,
            1950000000,
            400000000,
            2440000000,
            5590000000,
            '',
            11050000000
        ]
    }
    
    manufacturing_df = pd.DataFrame(manufacturing_data)
    manufacturing_file = data_dir / "制造业公司_资产负债表_2023.xlsx"
    manufacturing_df.to_excel(manufacturing_file, index=False, sheet_name="资产负债表")
    print(f"✅ 已创建示例文件: {manufacturing_file}")
    
    # 创建包含多个工作表的综合财报
    comprehensive_file = data_dir / "综合财报_ABC公司_2023.xlsx"
    
    with pd.ExcelWriter(comprehensive_file) as writer:
        # 资产负债表
        tech_df.to_excel(writer, sheet_name="资产负债表", index=False)
        
        # 利润表
        income_data = {
            '项目': [
                '营业收入',
                '营业成本',
                '营业税金及附加',
                '销售费用',
                '管理费用',
                '研发费用',
                '财务费用',
                '营业利润',
                '营业外收入',
                '营业外支出',
                '利润总额',
                '所得税费用',
                '净利润'
            ],
            '2023年度': [
                8500000000,
                5200000000,
                85000000,
                650000000,
                480000000,
                720000000,
                *********,
                1245000000,
                25000000,
                15000000,
                1255000000,
                188000000,
                1067000000
            ],
            '2022年度': [
                7800000000,
                4850000000,
                78000000,
                580000000,
                420000000,
                650000000,
                110000000,
                1112000000,
                20000000,
                12000000,
                1*********,
                168000000,
                952000000
            ]
        }
        
        income_df = pd.DataFrame(income_data)
        income_df.to_excel(writer, sheet_name="利润表", index=False)
        
        # 现金流量表
        cashflow_data = {
            '项目': [
                '经营活动现金流入',
                '销售商品、提供劳务收到的现金',
                '收到的税费返还',
                '收到其他与经营活动有关的现金',
                '经营活动现金流入小计',
                '',
                '经营活动现金流出',
                '购买商品、接受劳务支付的现金',
                '支付给职工以及为职工支付的现金',
                '支付的各项税费',
                '支付其他与经营活动有关的现金',
                '经营活动现金流出小计',
                '',
                '经营活动产生的现金流量净额',
                '',
                '投资活动产生的现金流量净额',
                '筹资活动产生的现金流量净额',
                '现金及现金等价物净增加额'
            ],
            '2023年度': [
                '',
                8800000000,
                45000000,
                180000000,
                9025000000,
                '',
                '',
                5800000000,
                850000000,
                420000000,
                380000000,
                7450000000,
                '',
                1575000000,
                '',
                -650000000,
                -775000000,
                *********
            ],
            '2022年度': [
                '',
                8*********,
                38000000,
                *********,
                8288000000,
                '',
                '',
                5400000000,
                780000000,
                380000000,
                320000000,
                6880000000,
                '',
                1408000000,
                '',
                -580000000,
                -720000000,
                108000000
            ]
        }
        
        cashflow_df = pd.DataFrame(cashflow_data)
        cashflow_df.to_excel(writer, sheet_name="现金流量表", index=False)
    
    print(f"✅ 已创建综合财报文件: {comprehensive_file}")
    
    print(f"\n📊 示例数据创建完成！")
    print(f"数据目录: {data_dir}")
    print("现在可以运行财报生成器了。")

if __name__ == "__main__":
    create_sample_financial_data()
