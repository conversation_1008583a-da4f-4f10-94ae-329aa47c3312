from distutils.version import <PERSON><PERSON><PERSON>ersion

import pytest

from pandas.compat._optional import VERSIONS

import pandas as pd
from pandas.core.computation.engines import _engines
import pandas.core.computation.expr as expr


def test_compat():
    # test we have compat with our version of nu

    from pandas.core.computation.check import _NUMEXPR_INSTALLED

    try:
        import numexpr as ne

        ver = ne.__version__
        if LooseVersion(ver) < LooseVersion(VERSIONS["numexpr"]):
            assert not _NUMEXPR_INSTALLED
        else:
            assert _NUMEXPR_INSTALLED
    except ImportError:
        pytest.skip("not testing numexpr version compat")


@pytest.mark.parametrize("engine", _engines)
@pytest.mark.parametrize("parser", expr._parsers)
def test_invalid_numexpr_version(engine, parser):
    def testit():
        a, b = 1, 2  # noqa
        res = pd.eval("a + b", engine=engine, parser=parser)
        assert res == 3

    if engine == "numexpr":
        try:
            import numexpr as ne
        except ImportError:
            pytest.skip("no numexpr")
        else:
            if LooseVersion(ne.__version__) < LooseVersion(VERSIONS["numexpr"]):
                with pytest.raises(ImportError):
                    testit()
            else:
                testit()
    else:
        testit()
