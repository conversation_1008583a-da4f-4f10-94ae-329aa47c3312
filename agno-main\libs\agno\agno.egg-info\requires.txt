docstring-parser
gitpython
httpx
pydantic-settings
pydantic
python-dotenv
python-multipart
pyyaml
rich
tomli
typer
typing-extensions

[agentql]
agentql

[agui]
ag-ui-protocol

[anthropic]
anthropic

[apify]
apify-client

[arize]
arize-phoenix
agno[opentelemetry]
opentelemetry-exporter-otlp-proto-grpc
opentelemetry-distro

[aws]
agno-aws
agno-docker

[azure]
azure-ai-inference
aiohttp

[brave]
brave-search

[browserbase]
browserbase
playwright

[cartesia]
cartesia

[cassandra]
cassio

[cerebras]
cerebras-cloud-sdk

[chromadb]
chromadb

[clickhouse]
clickhouse-connect

[cohere]
cohere

[confluence]
atlassian-python-api

[cookbooks]
inquirer
email_validator

[couchbase]
couchbase

[crawl4ai]
crawl4ai

[csv]
aiofiles

[ddg]
duckduckgo-search

[dev]
mypy
pytest
pytest-asyncio
pytest-cov
pytest-mock
ruff
timeout-decorator
types-pyyaml
types-aiofiles
fastapi
uvicorn
arxiv

[docker]
agno-docker

[docx]
python-docx

[duckdb]
duckdb

[elevenlabs]
elevenlabs

[embedders]
agno[huggingface]

[exa]
exa_py

[fal]
fal_client

[firecrawl]
firecrawl-py

[firestore]
google-cloud-firestore

[gcs]
google-cloud-storage

[github]
PyGithub

[gmail]
google-api-python-client
google-auth-httplib2
google-auth-oauthlib

[google]
google-genai

[google_bigquery]
google-cloud-bigquery

[googlemaps]
googlemaps
google-maps-places

[groq]
groq

[huggingface]
huggingface-hub

[ibm]
ibm-watsonx-ai

[infinity]
infinity_client

[integration-tests]
exa_py
duckduckgo-search
yfinance
sqlalchemy
Pillow

[knowledge]
agno[pdf]
agno[docx]
agno[text]
agno[csv]
agno[markdown]

[lancedb]
lancedb==0.20.0
tantivy

[langfuse]
langfuse

[litellm]
litellm

[lmstudio]
lmstudio

[markdown]
unstructured
markdown
aiofiles

[matplotlib]
matplotlib

[mcp]
mcp

[mem0]
mem0ai

[meta]
llama-api-client

[milvusdb]
pymilvus>=2.5.10

[mistral]
mistralai

[models]
agno[anthropic]
agno[azure]
agno[cerebras]
agno[cohere]
agno[infinity]
agno[google]
agno[groq]
agno[ibm]
agno[litellm]
agno[meta]
agno[mistral]
agno[ollama]
agno[openai]

[mongodb]
pymongo[srv]

[newspaper]
newspaper4k
lxml_html_clean

[ollama]
ollama

[openai]
openai

[opencv]
opencv-python

[openlit]
openlit
agno[opentelemetry]

[opentelemetry]
opentelemetry-sdk
opentelemetry-exporter-otlp

[pdf]
pypdf
rapidocr_onnxruntime

[performance]
memory_profiler

[pgvector]
pgvector

[pinecone]
pinecone==5.4.2

[postgres]
psycopg-binary
psycopg

[qdrant]
qdrant-client

[redis]
redis

[singlestore]
sqlalchemy

[sql]
sqlalchemy

[sqlite]
sqlalchemy

[storage]
agno[sql]
agno[postgres]
agno[sqlite]
agno[gcs]
agno[firestore]
agno[redis]

[tests]
agno[dev]
agno[models]
agno[tools]
agno[storage]
agno[vectordbs]
agno[knowledge]
agno[embedders]
agno[performance]
agno[cookbooks]
agno[agui]
twine
build

[text]
aiofiles

[todoist]
todoist-api-python

[tools]
agno[apify]
agno[brave]
agno[exa]
agno[cartesia]
agno[ddg]
agno[duckdb]
agno[newspaper]
agno[youtube]
agno[firecrawl]
agno[crawl4ai]
agno[github]
agno[gmail]
agno[googlemaps]
agno[todoist]
agno[matplotlib]
agno[elevenlabs]
agno[fal]
agno[webex]
agno[mcp]
agno[browserbase]
agno[agentql]
agno[opencv]
agno[confluence]
agno[zep]
agno[mem0]
agno[google_bigquery]

[vectordbs]
agno[pgvector]
agno[chromadb]
agno[lancedb]
agno[qdrant]
agno[couchbase]
agno[cassandra]
agno[mongodb]
agno[singlestore]
agno[weaviate]
agno[milvusdb]
agno[clickhouse]
agno[pinecone]

[weave]
weave

[weaviate]
weaviate-client

[webex]
webexpythonsdk

[yfinance]
yfinance

[youtube]
youtube_transcript_api

[zep]
zep-cloud
