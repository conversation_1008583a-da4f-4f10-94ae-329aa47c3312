from agno.agent import Agent, RunResponse  # noqa
from agno.models.sambanova import <PERSON><PERSON><PERSON>

agent = Agent(model=<PERSON><PERSON><PERSON>(id="Meta-Llama-3.1-8B-Instruct"), markdown=True)

# Get the response in a variable
# run: RunResponse = agent.run("Share a 2 sentence horror story")
# print(run.content)

# Print the response in the terminal
agent.print_response("Share a 2 sentence horror story")
