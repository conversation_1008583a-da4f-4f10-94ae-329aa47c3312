{# Licensed under the Apache License: http://www.apache.org/licenses/LICENSE-2.0 #}
{# For details: https://github.com/nedbat/coveragepy/blob/master/NOTICE.txt #}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    {# IE8 rounds line-height incorrectly, and adding this emulateIE7 line makes it right! #}
    {# http://social.msdn.microsoft.com/Forums/en-US/iewebdevelopment/thread/7684445e-f080-4d8f-8529-132763348e21 #}
    <meta http-equiv="X-UA-Compatible" content="IE=emulateIE7" />
    <title>Coverage for {{relative_filename|escape}}: {{nums.pc_covered_str}}%</title>
    <link rel="stylesheet" href="style.css" type="text/css">
    {% if extra_css %}
        <link rel="stylesheet" href="{{ extra_css }}" type="text/css">
    {% endif %}
    <script type="text/javascript" src="jquery.min.js"></script>
    <script type="text/javascript" src="jquery.hotkeys.js"></script>
    <script type="text/javascript" src="jquery.isonscreen.js"></script>
    <script type="text/javascript" src="coverage_html.js"></script>
    <script type="text/javascript">
        jQuery(document).ready(coverage.pyfile_ready);
    </script>
</head>
<body class="pyfile">

<div id="header">
    <div class="content">
        <h1>Coverage for <b>{{relative_filename|escape}}</b> :
            <span class="pc_cov">{{nums.pc_covered_str}}%</span>
        </h1>

        <img id="keyboard_icon" src="keybd_closed.png" alt="Show keyboard shortcuts" />

        <h2 class="stats">
            {{nums.n_statements}} statements &nbsp;
            <button type="button" class="{{category.run}} shortkey_r button_toggle_run" title="Toggle lines run">{{nums.n_executed}} run</button>
            <button type="button" class="{{category.mis}} shortkey_m button_toggle_mis" title="Toggle lines missing">{{nums.n_missing}} missing</button>
            <button type="button" class="{{category.exc}} shortkey_x button_toggle_exc" title="Toggle lines excluded">{{nums.n_excluded}} excluded</button>

            {% if has_arcs %}
            <button type="button" class="{{category.par}} shortkey_p button_toggle_par" title="Toggle lines partially run">{{nums.n_partial_branches}} partial</button>
            {% endif %}
        </h2>
    </div>
</div>

<div class="help_panel">
    <img id="panel_icon" src="keybd_open.png" alt="Hide keyboard shortcuts" />
    <p class="legend">Hot-keys on this page</p>
    <div>
    <p class="keyhelp">
        <span class="key">r</span>
        <span class="key">m</span>
        <span class="key">x</span>
        <span class="key">p</span> &nbsp; toggle line displays
    </p>
    <p class="keyhelp">
        <span class="key">j</span>
        <span class="key">k</span> &nbsp; next/prev highlighted chunk
    </p>
    <p class="keyhelp">
        <span class="key">0</span> &nbsp; (zero) top of page
    </p>
    <p class="keyhelp">
        <span class="key">1</span> &nbsp; (one) first highlighted chunk
    </p>
    </div>
</div>

<div id="source">
    {% for line in lines -%}
        {% joined %}
        <p id="t{{line.number}}" class="{{line.css_class}}">
            <span class="n"><a href="#t{{line.number}}">{{line.number}}</a></span>
            <span class="t">{{line.html}}&nbsp;</span>
            {% if line.context_list %}
                <input type="checkbox" id="ctxs{{line.number}}" />
            {% endif %}
            {# Things that should float right in the line. #}
            <span class="r">
                {% if line.annotate %}
                    <span class="annotate short">{{line.annotate}}</span>
                    <span class="annotate long">{{line.annotate_long}}</span>
                {% endif %}
                {% if line.contexts %}
                    <label for="ctxs{{line.number}}" class="ctx">{{ line.contexts_label }}</label>
                {% endif %}
            </span>
            {# Things that should appear below the line. #}
            {% if line.context_list %}
                <span class="ctxs">
                    {% for context in line.context_list %}
                        <span>{{context}}</span>
                    {% endfor %}
                </span>
            {% endif %}
        </p>
        {% endjoined %}
    {% endfor %}
</div>

<div id="footer">
    <div class="content">
        <p>
            <a class="nav" href="index.html">&#xab; index</a> &nbsp; &nbsp; <a class="nav" href="{{__url__}}">coverage.py v{{__version__}}</a>,
            created at {{ time_stamp }}
        </p>
    </div>
</div>

</body>
</html>
