"""
简化版财报HTML生成器
避免复杂依赖，专注核心功能
"""

import os
import json
from pathlib import Path
from textwrap import dedent

from agno.agent import Agent
from agno.models.ollama import Ollama

class SimpleFinancialGenerator:
    """简化版财报生成器"""
    
    def __init__(self):
        self.input_dir = Path("agno-exp/financial_data")
        self.output_dir = Path("agno-exp/html_reports")
        self.prompt_file = Path("agno-exp/financial_prompt.txt")
        
        # 创建目录
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化Agent
        self._init_agent()
    
    def _init_agent(self):
        """初始化Agent"""
        try:
            # 读取提示词
            if self.prompt_file.exists():
                with open(self.prompt_file, 'r', encoding='utf-8') as f:
                    prompt = f.read()
            else:
                prompt = self._get_default_prompt()
            
            # 配置模型
            model = Ollama(
                id="qwen3:0.6b",
                host="http://*************:11434",
                options={
                    "temperature": 0.2,
                    "top_p": 0.9,
                    "num_ctx": 4096,
                }
            )
            
            # 创建Agent
            self.agent = Agent(
                model=model,
                instructions=prompt,
                markdown=False,
            )
            
            print("✅ Agent初始化成功")
            
        except Exception as e:
            print(f"❌ Agent初始化失败: {e}")
            raise
    
    def _get_default_prompt(self):
        """获取默认提示词"""
        return dedent("""\
            你是一个专业的财报HTML页面生成专家。

            任务：根据提供的财报数据，生成一个完整的、美观的HTML页面。

            要求：
            1. 使用现代化的CSS设计，包含TailwindCSS
            2. 响应式布局，适配不同屏幕尺寸
            3. 突出显示关键财务指标
            4. 使用图表展示数据趋势
            5. 包含公司信息和报告期间
            6. 添加适当的动画效果

            输出：
            请直接输出完整的HTML代码，包含所有CSS和JavaScript，确保可以直接保存为.html文件并在浏览器中正常显示。
        """)
    
    def create_sample_data(self):
        """创建示例财报数据"""
        sample_data = {
            "company_name": "科技创新股份有限公司",
            "report_period": "2023年年度报告",
            "balance_sheet": {
                "assets": {
                    "current_assets": {
                        "cash": **********,
                        "accounts_receivable": **********,
                        "inventory": *********,
                        "other_current": *********,
                        "total": **********
                    },
                    "non_current_assets": {
                        "fixed_assets": **********,
                        "intangible_assets": **********,
                        "goodwill": **********,
                        "investments": **********,
                        "other_non_current": *********,
                        "total": **********
                    },
                    "total_assets": ***********
                },
                "liabilities": {
                    "current_liabilities": {
                        "short_term_debt": *********,
                        "accounts_payable": **********,
                        "accrued_expenses": *********,
                        "other_current": *********,
                        "total": **********
                    },
                    "non_current_liabilities": {
                        "long_term_debt": **********,
                        "bonds_payable": **********,
                        "other_non_current": *********,
                        "total": 3*********
                    },
                    "total_liabilities": 6*********
                },
                "equity": {
                    "share_capital": **********,
                    "capital_surplus": **********,
                    "retained_earnings": **********,
                    "total_equity": **********
                }
            },
            "income_statement": {
                "revenue": 8*********,
                "cost_of_sales": **********,
                "gross_profit": **********,
                "operating_expenses": 1850000000,
                "operating_profit": 1*********,
                "net_profit": 1067000000
            },
            "key_ratios": {
                "debt_to_equity": 0.98,
                "current_ratio": 1.31,
                "roe": 0.152,
                "gross_margin": 0.388,
                "net_margin": 0.126
            }
        }
        
        # 保存示例数据
        sample_file = self.input_dir / "sample_financial_data.json"
        with open(sample_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已创建示例数据: {sample_file}")
        return sample_data
    
    def generate_html_from_data(self, financial_data):
        """根据财报数据生成HTML"""
        try:
            # 构建发送给模型的提示
            data_text = json.dumps(financial_data, ensure_ascii=False, indent=2)
            
            prompt = f"""
            请根据以下财报数据生成一个完整的HTML页面：

            {data_text}

            要求：
            1. 创建一个现代化、专业的财报展示页面
            2. 使用TailwindCSS进行样式设计
            3. 包含公司基本信息、关键指标卡片、资产负债结构图、财务比率等
            4. 添加适当的颜色和动画效果
            5. 确保响应式设计
            6. 直接输出完整的HTML代码，包含所有必要的CSS和JavaScript

            请生成完整的HTML代码：
            """
            
            print("🤖 正在生成HTML页面...")
            response = self.agent.run(prompt)
            
            return response.content
            
        except Exception as e:
            print(f"❌ HTML生成失败: {e}")
            return None
    
    def save_html(self, html_content, filename="financial_report.html"):
        """保存HTML文件"""
        try:
            output_file = self.output_dir / filename
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML文件已保存: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None
    
    def run_demo(self):
        """运行演示"""
        print("🚀 财报HTML生成器演示")
        print("=" * 50)
        
        # 1. 创建示例数据
        print("1. 创建示例财报数据...")
        financial_data = self.create_sample_data()
        
        # 2. 生成HTML
        print("\n2. 生成HTML页面...")
        html_content = self.generate_html_from_data(financial_data)
        
        if html_content:
            # 3. 保存HTML文件
            print("\n3. 保存HTML文件...")
            output_file = self.save_html(html_content, "demo_financial_report.html")
            
            if output_file:
                print(f"\n🎉 演示完成！")
                print(f"📄 HTML文件: {output_file}")
                print(f"🌐 在浏览器中打开查看效果")
            else:
                print("❌ 保存失败")
        else:
            print("❌ HTML生成失败")

def main():
    """主函数"""
    try:
        generator = SimpleFinancialGenerator()
        generator.run_demo()
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
