"""
简化的PDF知识库示例 - 避免LanceDB问题
使用内存向量数据库或其他更稳定的方案
"""

from textwrap import dedent
import os

from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.models.ollama import Ollama

def create_simple_pdf_agent(pdf_path: str):
    """
    创建简单的PDF知识库Agent，不使用复杂的向量数据库
    """
    
    # 检查PDF文件
    if not os.path.exists(pdf_path):
        print(f"❌ 错误: PDF文件不存在: {pdf_path}")
        return None
    
    print(f"📄 加载PDF文件: {pdf_path}")
    
    # 配置Ollama模型
    ollama_model = Ollama(
        id="qwen3:0.6b",
        host="http://*************:11434",
        options={
            "temperature": 0.1,  # 降低温度以获得更准确的回答
        }
    )
    
    # 方案1: 使用简单的PDF读取，不使用向量数据库
    try:
        # 直接读取PDF内容
        pdf_reader = PDFReader(
            chunk=True,
            chunk_size=1000,
            chunk_overlap=100,
        )
        
        # 读取PDF文档
        documents = pdf_reader.read(pdf=pdf_path)
        
        # 将文档内容合并为上下文
        pdf_content = "\n\n".join([doc.content for doc in documents if doc.content])
        
        print(f"✅ 成功读取PDF，共 {len(documents)} 个文档块")
        print(f"📊 总字符数: {len(pdf_content)}")
        
        # 创建带有PDF内容的Agent
        agent = Agent(
            model=ollama_model,
            instructions=dedent(f"""\
                你是一个专业的文档助手，专门回答基于以下PDF文档内容的问题。
                
                PDF文档内容：
                {pdf_content[:5000]}...  # 只显示前5000字符作为示例
                
                工作原则：
                1. 仅基于上述PDF文档内容回答问题
                2. 如果问题超出文档范围，明确告知用户
                3. 回答要准确、详细，并尽可能引用文档中的具体内容
                4. 保持专业和友好的语调
                
                回答格式：
                - 首先说明基于文档内容回答
                - 提供详细的回答
                - 如果相关，引用文档中的具体段落
            """),
            markdown=True,
        )
        
        return agent, pdf_content
        
    except Exception as e:
        print(f"❌ PDF读取失败: {e}")
        return None, None

def create_knowledge_base_agent(pdf_path: str):
    """
    方案2: 使用知识库但配置更保守的参数
    """
    
    if not os.path.exists(pdf_path):
        print(f"❌ 错误: PDF文件不存在: {pdf_path}")
        return None
    
    try:
        # 使用内存向量数据库（如果可用）
        from agno.vectordb.lancedb import LanceDb
        from agno.embedder.ollama import OllamaEmbedder
        
        # 使用相同的模型作为嵌入器
        embedder = OllamaEmbedder(
            id="qwen3:0.6b",
            host="http://*************:11434"
        )
        
        # 配置向量数据库
        vector_db = LanceDb(
            uri="tmp/simple_pdf_db",
            table_name="pdf_docs",
            embedder=embedder
        )
        
        # 创建知识库
        knowledge_base = PDFKnowledgeBase(
            path=pdf_path,
            reader=PDFReader(
                chunk=True,
                chunk_size=300,  # 更小的分块
                chunk_overlap=30,
            ),
            vector_db=vector_db,
        )
        
        # 配置模型
        ollama_model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        # 创建Agent
        agent = Agent(
            model=ollama_model,
            knowledge=knowledge_base,
            instructions=dedent("""\
                你是一个专业的文档助手，能够基于PDF文档内容回答问题。
                
                工作原则：
                1. 优先使用PDF文档中的信息回答问题
                2. 如果文档中没有相关信息，明确告知用户
                3. 回答要准确、详细
                4. 保持专业和友好的语调
            """),
            markdown=True,
            search_knowledge=True,
        )
        
        return agent
        
    except Exception as e:
        print(f"❌ 知识库创建失败: {e}")
        return None

def test_agent(agent, pdf_content=None):
    """测试Agent功能"""
    
    questions = [
        "这个文档的主要内容是什么？",
        "如何访问装备系统?",
        "车型规则怎么设置?",
        "车型组合测算怎么发布?",
    ]
    
    print("\n📝 开始测试问答...")
    print("=" * 50)
    
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. 问题: {question}")
        print("-" * 30)
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            print(f"❌ 回答失败: {e}")
        print()

def main():
    """主函数"""
    print("📚 简化PDF知识库示例")
    print("=" * 50)
    
    pdf_path = "./pur.pdf"
    
    # 检查PDF文件
    if not os.path.exists(pdf_path):
        print(f"❌ 找不到PDF文件: {pdf_path}")
        print("请确保PDF文件在当前目录下")
        return
    
    print("🔄 尝试方案1: 直接读取PDF内容...")
    agent, pdf_content = create_simple_pdf_agent(pdf_path)
    
    if agent:
        print("✅ 方案1成功，开始测试...")
        test_agent(agent, pdf_content)
    else:
        print("❌ 方案1失败，尝试方案2...")
        
        print("🔄 尝试方案2: 使用知识库...")
        agent = create_knowledge_base_agent(pdf_path)
        
        if agent:
            print("🔄 加载知识库...")
            try:
                agent.knowledge.load(recreate=False)
                print("✅ 方案2成功，开始测试...")
                test_agent(agent)
            except Exception as e:
                print(f"❌ 知识库加载失败: {e}")
        else:
            print("❌ 所有方案都失败了")

if __name__ == "__main__":
    main()
