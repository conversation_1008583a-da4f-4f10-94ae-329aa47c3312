
from textwrap import dedent

from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader  # 本地PDF知识库
from agno.models.ollama import Ollama
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb, SearchType
from agno.embedder.ollama import OllamaEmbedder


# 使用更稳定的嵌入模型
ollama_embedder = OllamaEmbedder(
    id="qwen3:0.6b",  # 使用与主模型相同的模型作为嵌入器
    host="http://*************:11434"
)


ollama_model = Ollama(
    id="qwen3:0.6b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)


# Create a Recipe Expert Agent with knowledge of Thai recipes
agent = Agent(
    model=ollama_model,
    instructions=dedent("""\
    您是一位装备管理领域的资深专家，对装备平台及装备条线的操作与管理有着深入的了解。您的专业知识主要来源于一份详尽的操作手册，该手册是您回答用户问题的权威依据。

    当用户提出问题时，请首先参考操作手册中的内容进行回答。从手册中提取与问题最直接相关的信息，并进行整合与提炼。若手册中信息不完整或用户问题涉及手册之外的领域，回答”抱歉，目前无法还无法回复您的提问“。在回答之前，请确保所提供信息的准确性。

    请使用专业术语，保持礼貌与耐心，为用户提供清晰、有条理的回答。回答应包含明确的引言、主体内容以及总结。

    示例回答框架：
    - 引言：“您好，关于您提出的关于装备平台/装备条线的问题，我根据操作手册为您提供以下回答：”
    - 主体内容：[详细描述操作步骤或管理流程，强调关键节点和注意事项，提供可能的解决方案或替代方案]
    - 总结：“希望以上回答能对您有所帮助。如有其他问题或需要更多信息，请随时告诉我。”\
    """),


    knowledge=PDFKnowledgeBase(
        path="./pur.pdf",  # 本地PDF文件路径
        reader=PDFReader(
            chunk=True,  # 启用分块读取
            chunk_size=500,  # 减小分块大小避免处理问题
            chunk_overlap=50,  # 减小重叠
        ),
        vector_db=LanceDb(
            uri="tmp/lancedb_equipment",  # 使用新的数据库路径
            table_name="equipment_docs",
            search_type=SearchType.vector,  # 使用简单的向量搜索而不是混合搜索
            embedder=ollama_embedder
        ),
    ),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

# 安全加载知识库
def load_knowledge_safely():
    """安全加载知识库，包含错误处理"""
    if agent.knowledge is None:
        print("❌ 知识库未配置")
        return False

    try:
        print("🔄 正在加载PDF知识库...")
        agent.knowledge.load(recreate=False)  # 避免重复创建
        print("✅ 知识库加载成功")
        return True
    except Exception as e:
        print(f"❌ 知识库加载失败: {e}")
        print("💡 建议:")
        print("1. 检查PDF文件是否存在且可读")
        print("2. 确保Ollama服务正常运行")
        print("3. 尝试删除 tmp/lancedb_equipment 目录后重新运行")
        return False

def test_questions():
    """测试问答功能"""
    questions = [
        "如何访问装备系统?",
        "车型规则怎么设置?",
        "车型组合测算怎么发布?"
    ]

    print("\n📝 开始测试问答...")
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. 问题: {question}")
        print("-" * 30)
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            print(f"❌ 回答失败: {e}")
        print()

# 主执行逻辑
if __name__ == "__main__":
    print("📚 装备管理PDF知识库系统")
    print("=" * 50)

    # 检查PDF文件
    import os
    if not os.path.exists("./pur.pdf"):
        print("❌ 错误: 找不到 pur.pdf 文件")
        print("请确保PDF文件在当前目录下")
        exit(1)

    # 加载知识库
    if load_knowledge_safely():
        # 运行测试
        test_questions()
    else:
        print("❌ 无法继续，请解决知识库加载问题")

