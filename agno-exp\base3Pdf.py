
from textwrap import dedent
import os
from pathlib import Path

from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader  # 改为本地PDF知识库
from agno.models.ollama import Ollama
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb, SearchType
from agno.embedder.ollama import OllamaEmbedder


ollama_embedder = OllamaEmbedder(
    id="llama3.2-vision:11b",
    host="http://*************:11434"
)


ollama_model = Ollama(
    id="qwen3:0.6b",  # 模型名称，必须与ollama pull的模型名称完全一致
    host="http://*************:11434",  # 使用host参数而不是api_base
)


# Create a Recipe Expert Agent with knowledge of Thai recipes
agent = Agent(
    model=ollama_model,
    instructions=dedent("""\
    您是一位装备管理领域的资深专家，对装备平台及装备条线的操作与管理有着深入的了解。您的专业知识主要来源于一份详尽的操作手册，该手册是您回答用户问题的权威依据。

    当用户提出问题时，请首先参考操作手册中的内容进行回答。从手册中提取与问题最直接相关的信息，并进行整合与提炼。若手册中信息不完整或用户问题涉及手册之外的领域，回答”抱歉，目前无法还无法回复您的提问“。在回答之前，请确保所提供信息的准确性。

    请使用专业术语，保持礼貌与耐心，为用户提供清晰、有条理的回答。回答应包含明确的引言、主体内容以及总结。

    示例回答框架：
    - 引言：“您好，关于您提出的关于装备平台/装备条线的问题，我根据操作手册为您提供以下回答：”
    - 主体内容：[详细描述操作步骤或管理流程，强调关键节点和注意事项，提供可能的解决方案或替代方案]
    - 总结：“希望以上回答能对您有所帮助。如有其他问题或需要更多信息，请随时告诉我。”\
    """),


    knowledge=PDFKnowledgeBase(
        path="./pur.pdf",  # 本地PDF文件路径
        reader=PDFReader(chunk=True),  # 启用分块读取
        vector_db=LanceDb(
            uri="tmp/lancedb",
            table_name="equipment_knowledge",  # 更改表名以反映内容
            search_type=SearchType.hybrid,
            embedder=ollama_embedder
        ),
    ),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

# Comment out after the knowledge base is loaded
if agent.knowledge is not None:
    agent.knowledge.load()

agent.print_response(
    "如何访问装备系统?", stream=True
)
agent.print_response("车型规则怎么设置?", stream=True)
agent.print_response("车型组合测算怎么发布?", stream=True)

