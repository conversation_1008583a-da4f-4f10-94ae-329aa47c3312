"""
PDF知识库示例 - 使用本地PDF文件
"""

from textwrap import dedent

from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.models.ollama import Ollama
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb, SearchType
from agno.embedder.ollama import OllamaEmbedder

# 配置嵌入模型
ollama_embedder = OllamaEmbedder(
    id="qwen3:0.6b",  # 使用与主模型相同的模型作为嵌入器
    host="http://*************:11434"
)

# 配置主模型
ollama_model = Ollama(
    id="qwen3:0.6b",
    host="http://*************:11434",
)

# 创建Agent
agent = Agent(
    model=ollama_model,
    tools=[DuckDuckGoTools()],
    instructions=dedent("""\
        你是一个专业的文档助手，能够基于PDF文档内容回答用户问题。
        
        工作原则：
        1. 优先使用PDF文档中的信息回答问题
        2. 如果文档中没有相关信息，明确告知用户
        3. 回答要准确、详细，并引用文档中的具体内容
        4. 保持专业和友好的语调
    """),
    knowledge=PDFKnowledgeBase(
        path="agno-exp/pur.pdf",  # 本地PDF文件路径
        reader=PDFReader(chunk=True, chunk_size=1000),
        vector_db=LanceDb(
            uri="tmp/lancedb_pdf",
            table_name="pdf_knowledge",
            search_type=SearchType.vector,
            embedder=ollama_embedder
        ),
    ),
    search_knowledge=True,
    markdown=True,
)

# 安全加载知识库
def load_knowledge_safely():
    """安全加载知识库，包含错误处理"""
    if agent.knowledge is None:
        print("❌ 知识库未配置")
        return False
    
    try:
        print("🔄 正在加载PDF知识库...")
        agent.knowledge.load(recreate=False)  # 避免重复创建
        print("✅ 知识库加载成功")
        return True
    except Exception as e:
        print(f"❌ 知识库加载失败: {e}")
        print("💡 建议:")
        print("1. 检查PDF文件是否存在且可读")
        print("2. 确保Ollama服务正常运行")
        print("3. 尝试删除 tmp/ 目录后重新运行")
        return False

def test_questions():
    """测试问答功能"""
    questions = [
        "这个文档的主要内容是什么？",
        "如何访问装备系统?",
        "车型规则怎么设置?", 
        "车型组合测算怎么发布?"
    ]
    
    print("\n📝 开始测试问答...")
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. 问题: {question}")
        print("-" * 30)
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            print(f"❌ 回答失败: {e}")
        print()

# 主执行逻辑
if __name__ == "__main__":
    print("📚 PDF知识库示例")
    print("=" * 50)
    
    # 检查PDF文件
    import os
    if not os.path.exists("agno-exp/pur.pdf"):
        print("❌ 错误: 找不到 pur.pdf 文件")
        print("请确保PDF文件在 agno-exp 目录下")
        exit(1)
    
    # 加载知识库
    if load_knowledge_safely():
        # 运行测试
        test_questions()
    else:
        print("❌ 无法继续，请解决知识库加载问题")
