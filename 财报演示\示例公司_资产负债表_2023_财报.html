<think>
好的，用户让我根据提供的财报数据生成一个完整的HTML页面。首先，我需要仔细分析用户的需求。用户要求使用现代化的CSS设计，响应式布局，突出关键财务指标，卡片或表格布局，添加公司名称和报告期间，并且确保页面美观易读。

首先，我需要确认用户提供的数据结构。表格中有流动资产、非流动资产、总资产等项目，以及对应的数值。用户的数据看起来已经整理好了，但需要转化为HTML结构。接下来，我需要考虑如何组织这些数据，比如用卡片或表格展示，同时保持美观。

用户提到要使用卡片布局，所以可能需要将每个财务指标作为卡片展示。例如，流动资产的数值可以作为卡片，包含项目和数值。同时，需要突出显示关键数据，比如总资产、负债和所有者权益等。还要注意响应式布局，确保在不同设备上显示良好。

接下来，我需要检查数据中的NaN值，可能用户的数据中存在缺失值，但根据提供的数据，流动资产和货币资金的数值都是正确的，所以不需要处理这些。然后，考虑公司名称和报告期间，用户提到“示例公司”，所以需要在页面中添加公司名称，并注明报告期间2023年。

然后，我需要确保所有财务指标正确显示，并且用卡片或表格布局。可能需要使用CSS来美化，比如使用不同的颜色和字体，使页面看起来专业。同时，注意响应式设计，比如调整列宽和网格布局，确保在手机或平板上也能良好显示。

最后，验证整个HTML代码是否正确，没有语法错误，并且所有要求都被满足。可能还需要检查数据是否正确无误，确保所有财务指标的数值正确，没有遗漏或错误。完成后，将代码直接输出，确保用户可以直接保存为.html文件在浏览器中查看。
</think>

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>示例公司资产负债表</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f4f4f4;
    }
    header {
      background-color: #007BFF;
      color: white;
      padding: 15px;
      text-align: center;
    }
    h1 {
      margin: 0;
    }
    .card {
      background-color: #ffffff;
      border: 1px solid #ccc;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .card h2 {
      margin-bottom: 5px;
    }
    .card .value {
      font-weight: bold;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #007BFF;
      color: white;
    }
    .footer {
      text-align: center;
      font-size: 0.9em;
      margin-top: 20px;
    }
    @media (max-width: 600px) {
      table, th, td {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>示例公司</h1>
    <p>2023年资产负债表</p>
  </header>

  <div class="card">
    <h2>流动资产</h2>
    <div class="value">408000.0</div>
  </div>

  <div class="card">
    <h2>货币资金</h2>
    <div class="value">125000.0</div>
  </div>

  <div class="card">
    <h2>应收账款</h2>
    <div class="value">210000.0</div>
  </div>

  <div class="card">
    <h2>存货</h2>
    <div class="value">45000.0</div>
  </div>

  <div class="card">
    <h2>其他流动资产</h2>
    <div class="value">28000.0</div>
  </div>

  <div class="card">
    <h2>流动资产合计</h2>
    <div class="value">408000.0</div>
  </div>

  <div class="card">
    <h2>非流动资产</h2>
    <div class="value">610000.0</div>
  </div>

  <div class="card">
    <h2>固定资产</h2>
    <div class="value">320000.0</div>
  </div>

  <div class="card">
    <h2>无形资产</h2>
    <div class="value">110000.0</div>
  </div>

  <div class="card">
    <h2>长期投资</h2>
    <div class="value">180000.0</div>
  </div>

  <div class="card">
    <h2>非流动资产合计</h2>
    <div class="value">610000.0</div>
  </div>

  <div class="card">
    <h2>总资产</h2>
    <div class="value">1018000.0</div>
  </div>

  <div class="card">
    <h2>负债和所有者权益</h2>
    <div class="value">450000.0</div>
  </div>

  <div class="card">
    <h2>负债</h2>
    <div class="value">250000.0</div>
  </div>

  <div class="card">
    <h2>短期借款</h2>
    <div class="value">80000.0</div>
  </div>

  <div class="card">
    <h2>应付账款</h2>
    <div class="value">120000.0</div>
  </div>

  <div class="card">
    <h2>其他流动负债</h2>
    <div class="value">50000.0</div>
  </div>

  <div class="card">
    <h2>流动负债合计</h2>
    <div class="value">212000.0</div>
  </div>

  <div class="card">
    <h2>非流动负债</h2>
    <div class="value">200000.0</div>
  </div>

  <div class="card">
    <h2>长期借款</h2>
    <div class="value">200000.0</div>
  </div>

  <div class="card">
    <h2>所有者权益</h2>
    <div class="value">568000.0</div>
  </div>

  <div class="card">
    <h2>所有者权益合计</h2>
    <div class="value">516000.0</div>
  </div>

  <div class="footer">
    <p>2023年资产负债表</p>
  </div>
</body>
</html>
```