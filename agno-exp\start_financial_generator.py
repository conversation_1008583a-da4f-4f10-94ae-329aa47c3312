"""
财报HTML生成器启动脚本
让用户选择不同的版本和功能
"""

import os
import sys
from pathlib import Path

def show_menu():
    """显示主菜单"""
    print("🏢 上市公司财报数据HTML页面生成系统")
    print("=" * 60)
    print()
    print("请选择要使用的功能：")
    print()
    print("1. 🔧 财报HTML生成器 - 综合分析多个Excel文件")
    print("2. 📝 创建示例数据 - 生成测试用的Excel文件")
    print("3. ❓ 帮助说明")
    print("4. 🚪 退出程序")
    print()

def run_financial_generator():
    """运行财报HTML生成器"""
    print("🚀 启动财报HTML生成器...")
    try:
        from interactive_financial_generator import InteractiveFinancialGenerator
        generator = InteractiveFinancialGenerator()
        generator.run()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 interactive_financial_generator.py 文件存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def create_sample_data():
    """创建示例数据"""
    print("🚀 创建示例数据...")
    try:
        from create_sample_data import create_sample_financial_data
        create_sample_financial_data()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 create_sample_data.py 文件存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")



def show_help():
    """显示帮助信息"""
    print("📖 帮助说明")
    print("=" * 50)
    print()
    print("🎯 功能说明：")
    print()
    print("1. 财报HTML生成器：")
    print("   - 读取指定目录下的所有Excel财报文件")
    print("   - 将多个文件作为数据源进行综合分析")
    print("   - 生成一个包含对比分析的HTML页面")
    print("   - 支持用户自定义工作目录")
    print("   - 适合实际财报分析工作")
    print()
    print("2. 创建示例数据：")
    print("   - 生成多个示例Excel文件")
    print("   - 包含不同公司的资产负债表数据")
    print("   - 用于测试综合分析功能")
    print()
    print("📋 使用要求：")
    print("- Python 3.7+")
    print("- 已安装 agno, ollama, pandas, openpyxl")
    print("- Ollama服务运行在 http://10.122.17.186:11434")
    print("- 模型 qwen3:0.6b 已下载")
    print()
    print("📁 目录结构：")
    print("- Excel文件：放在指定的工作目录中")
    print("- HTML输出：保存在同一工作目录中")
    print("- 提示词文件：可自定义修改")
    print()
    print("🔧 故障排除：")
    print("- 如果AI连接失败，请检查Ollama服务状态")
    print("- 如果Excel读取失败，请检查文件格式和权限")
    print("- 如果HTML生成质量不佳，可以修改提示词文件")
    print()

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = [
        ('agno', 'Agno框架'),
        ('pandas', 'Excel处理'),
        ('pathlib', 'Python标准库'),
    ]
    
    missing = []
    for module, desc in required_modules:
        try:
            __import__(module)
            print(f"✅ {desc}: 已安装")
        except ImportError:
            print(f"❌ {desc}: 未安装")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  缺少依赖: {', '.join(missing)}")
        print("请运行: pip install " + " ".join(missing))
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def main():
    """主函数"""
    try:
        while True:
            show_menu()
            
            choice = input("请选择功能 (1-4): ").strip()

            if choice == "1":
                print("\n" + "="*60)
                run_financial_generator()
            elif choice == "2":
                print("\n" + "="*60)
                create_sample_data()
            elif choice == "3":
                print("\n" + "="*60)
                show_help()
            elif choice == "4":
                print("\n👋 感谢使用，再见！")
                break
            else:
                print("\n❌ 无效选择，请重新输入")

            if choice in ["1", "2"]:
                input("\n按回车键返回主菜单...")
            
            print("\n" * 2)
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    # 检查依赖
    if check_dependencies():
        main()
    else:
        print("\n❌ 依赖检查失败，程序退出")
        sys.exit(1)
