"""
财报HTML生成器启动脚本
让用户选择不同的版本和功能
"""

import os
import sys
from pathlib import Path

def show_menu():
    """显示主菜单"""
    print("🏢 上市公司财报数据HTML页面生成系统")
    print("=" * 60)
    print()
    print("请选择要使用的功能：")
    print()
    print("1. 📊 演示版 - 快速体验（自动创建示例数据）")
    print("2. 🔧 交互版 - 完整功能（处理用户Excel文件）")
    print("3. 📝 创建示例数据 - 生成测试用的Excel文件")
    print("4. 🧪 基础测试 - 测试AI连接和基本功能")
    print("5. ❓ 帮助说明")
    print("6. 🚪 退出程序")
    print()

def run_demo():
    """运行演示版"""
    print("🚀 启动演示版...")
    try:
        from demo_financial_generator import DemoFinancialGenerator
        generator = DemoFinancialGenerator()
        generator.run_demo()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 demo_financial_generator.py 文件存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_interactive():
    """运行交互版"""
    print("🚀 启动交互版...")
    try:
        from interactive_financial_generator import InteractiveFinancialGenerator
        generator = InteractiveFinancialGenerator()
        generator.run()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 interactive_financial_generator.py 文件存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def create_sample_data():
    """创建示例数据"""
    print("🚀 创建示例数据...")
    try:
        from create_sample_data import create_sample_financial_data
        create_sample_financial_data()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 create_sample_data.py 文件存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_basic_test():
    """运行基础测试"""
    print("🚀 启动基础测试...")
    try:
        from test_financial_basic import test_basic_generation
        test_basic_generation()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 test_financial_basic.py 文件存在")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def show_help():
    """显示帮助信息"""
    print("📖 帮助说明")
    print("=" * 50)
    print()
    print("🎯 功能说明：")
    print()
    print("1. 演示版：")
    print("   - 自动创建示例财报数据")
    print("   - 快速生成HTML页面")
    print("   - 适合初次体验")
    print()
    print("2. 交互版：")
    print("   - 支持用户自定义目录")
    print("   - 处理用户提供的Excel文件")
    print("   - 批量生成HTML报告")
    print("   - 适合实际使用")
    print()
    print("3. 创建示例数据：")
    print("   - 生成多个示例Excel文件")
    print("   - 包含资产负债表、利润表等")
    print("   - 用于测试和学习")
    print()
    print("4. 基础测试：")
    print("   - 测试AI模型连接")
    print("   - 验证基本功能")
    print("   - 排查问题")
    print()
    print("📋 使用要求：")
    print("- Python 3.7+")
    print("- 已安装 agno, ollama, pandas, openpyxl")
    print("- Ollama服务运行在 http://10.122.17.186:11434")
    print("- 模型 qwen3:0.6b 已下载")
    print()
    print("📁 目录结构：")
    print("- Excel文件：放在指定的工作目录中")
    print("- HTML输出：保存在同一工作目录中")
    print("- 提示词文件：可自定义修改")
    print()
    print("🔧 故障排除：")
    print("- 如果AI连接失败，请检查Ollama服务状态")
    print("- 如果Excel读取失败，请检查文件格式和权限")
    print("- 如果HTML生成质量不佳，可以修改提示词文件")
    print()

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = [
        ('agno', 'Agno框架'),
        ('pandas', 'Excel处理'),
        ('pathlib', 'Python标准库'),
    ]
    
    missing = []
    for module, desc in required_modules:
        try:
            __import__(module)
            print(f"✅ {desc}: 已安装")
        except ImportError:
            print(f"❌ {desc}: 未安装")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  缺少依赖: {', '.join(missing)}")
        print("请运行: pip install " + " ".join(missing))
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def main():
    """主函数"""
    try:
        while True:
            show_menu()
            
            choice = input("请选择功能 (1-6): ").strip()
            
            if choice == "1":
                print("\n" + "="*60)
                run_demo()
            elif choice == "2":
                print("\n" + "="*60)
                run_interactive()
            elif choice == "3":
                print("\n" + "="*60)
                create_sample_data()
            elif choice == "4":
                print("\n" + "="*60)
                run_basic_test()
            elif choice == "5":
                print("\n" + "="*60)
                show_help()
            elif choice == "6":
                print("\n👋 感谢使用，再见！")
                break
            else:
                print("\n❌ 无效选择，请重新输入")
            
            if choice in ["1", "2", "3", "4"]:
                input("\n按回车键返回主菜单...")
            
            print("\n" * 2)
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    # 检查依赖
    if check_dependencies():
        main()
    else:
        print("\n❌ 依赖检查失败，程序退出")
        sys.exit(1)
