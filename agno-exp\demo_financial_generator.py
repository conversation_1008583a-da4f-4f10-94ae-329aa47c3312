"""
演示版财报生成器
快速创建示例数据并生成HTML
"""

import os
import json
import pandas as pd
from pathlib import Path
from textwrap import dedent

from agno.agent import Agent
from agno.models.ollama import Ollama

class DemoFinancialGenerator:
    """演示版财报生成器"""
    
    def __init__(self):
        self.work_dir = None
        
    def get_work_directory(self):
        """获取工作目录"""
        print("📁 财报HTML生成器 - 演示版")
        print("=" * 40)
        
        print("\n请选择工作目录：")
        print("1. 使用当前目录下的 '财报演示' 文件夹")
        print("2. 输入自定义目录路径")
        
        while True:
            choice = input("\n请选择 (1-2): ").strip()
            
            if choice == "1":
                self.work_dir = Path.cwd() / "财报演示"
                break
            elif choice == "2":
                custom_path = input("请输入目录路径: ").strip()
                if custom_path:
                    self.work_dir = Path(custom_path).resolve()
                    break
                else:
                    print("❌ 路径不能为空")
            else:
                print("❌ 无效选择")
        
        # 创建目录
        try:
            self.work_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 工作目录: {self.work_dir}")
        except Exception as e:
            print(f"❌ 目录创建失败: {e}")
            raise
    
    def create_sample_excel(self):
        """创建示例Excel文件"""
        print("\n📊 创建示例Excel文件...")
        
        # 示例财报数据
        financial_data = {
            '项目': [
                '流动资产',
                '货币资金',
                '应收账款',
                '存货',
                '其他流动资产',
                '流动资产合计',
                '',
                '非流动资产', 
                '固定资产',
                '无形资产',
                '长期投资',
                '非流动资产合计',
                '',
                '资产总计',
                '',
                '流动负债',
                '短期借款',
                '应付账款',
                '其他流动负债',
                '流动负债合计',
                '',
                '非流动负债',
                '长期借款',
                '非流动负债合计',
                '',
                '负债合计',
                '',
                '所有者权益',
                '股本',
                '资本公积',
                '未分配利润',
                '所有者权益合计',
                '',
                '负债和所有者权益总计'
            ],
            '2023年12月31日(万元)': [
                '',
                125000,
                210000,
                45000,
                28000,
                408000,
                '',
                '',
                320000,
                110000,
                180000,
                610000,
                '',
                1018000,
                '',
                '',
                80000,
                120000,
                50000,
                250000,
                '',
                '',
                200000,
                200000,
                '',
                450000,
                '',
                '',
                100000,
                420000,
                48000,
                568000,
                '',
                1018000
            ],
            '2022年12月31日(万元)': [
                '',
                110000,
                185000,
                38000,
                25000,
                358000,
                '',
                '',
                290000,
                95000,
                165000,
                550000,
                '',
                908000,
                '',
                '',
                65000,
                105000,
                42000,
                212000,
                '',
                '',
                180000,
                180000,
                '',
                392000,
                '',
                '',
                100000,
                380000,
                36000,
                516000,
                '',
                908000
            ]
        }
        
        # 创建Excel文件
        excel_file = self.work_dir / "示例公司_资产负债表_2023.xlsx"
        
        try:
            df = pd.DataFrame(financial_data)
            df.to_excel(excel_file, index=False, sheet_name="资产负债表")
            print(f"✅ 已创建示例Excel: {excel_file.name}")
            return excel_file
        except Exception as e:
            print(f"❌ Excel创建失败: {e}")
            return None
    
    def init_agent(self):
        """初始化AI Agent"""
        print("\n🤖 初始化AI模型...")
        
        try:
            model = Ollama(
                id="qwen3:0.6b",
                host="http://*************:11434",
                options={
                    "temperature": 0.1,
                    "num_ctx": 4096,
                }
            )
            
            prompt = dedent("""\
                你是一个专业的财报HTML页面生成专家。
                
                任务：根据提供的财报数据，生成一个完整的、美观的HTML页面。
                
                要求：
                1. 使用现代化的CSS设计
                2. 响应式布局
                3. 突出显示关键财务指标
                4. 使用卡片布局展示数据
                5. 添加适当的颜色和样式
                6. 包含公司信息标题
                
                输出：
                请直接输出完整的HTML代码，包含所有CSS样式，确保可以直接保存为.html文件并在浏览器中正常显示。
            """)
            
            self.agent = Agent(
                model=model,
                instructions=prompt,
                markdown=False,
            )
            
            print("✅ AI模型初始化成功")
            
        except Exception as e:
            print(f"❌ AI模型初始化失败: {e}")
            raise
    
    def process_excel_to_html(self, excel_file):
        """处理Excel文件并生成HTML"""
        print(f"\n📄 处理Excel文件: {excel_file.name}")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            excel_data = df.to_string(index=False)
            
            print("✅ Excel文件读取成功")
            
            # 生成HTML
            print("🤖 正在生成HTML...")
            
            prompt = f"""
            请根据以下财报数据生成一个完整的HTML页面：
            
            文件名: {excel_file.name}
            数据内容:
            {excel_data}
            
            要求：
            1. 创建一个专业的财报展示页面
            2. 使用现代化的CSS样式
            3. 突出显示重要的财务数据
            4. 添加公司名称和报告期间
            5. 使用卡片或表格布局
            6. 确保页面美观且易读
            
            请生成完整的HTML代码：
            """
            
            response = self.agent.run(prompt)
            html_content = response.content
            
            print("✅ HTML生成成功")
            
            # 保存HTML文件
            html_file = self.work_dir / f"{excel_file.stem}_财报.html"
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML文件已保存: {html_file.name}")
            
            return html_file
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return None
    
    def run_demo(self):
        """运行演示"""
        try:
            print("🏢 财报HTML生成器 - 演示版")
            print("=" * 50)
            
            # 1. 获取工作目录
            self.get_work_directory()
            
            # 2. 创建示例Excel文件
            excel_file = self.create_sample_excel()
            if not excel_file:
                return
            
            # 3. 初始化AI模型
            self.init_agent()
            
            # 4. 处理Excel并生成HTML
            html_file = self.process_excel_to_html(excel_file)
            
            if html_file:
                print(f"\n🎉 演示完成！")
                print(f"📁 工作目录: {self.work_dir}")
                print(f"📊 Excel文件: {excel_file.name}")
                print(f"🌐 HTML文件: {html_file.name}")
                print(f"\n💡 请在浏览器中打开HTML文件查看效果")
            else:
                print(f"\n❌ 演示失败")
                
        except KeyboardInterrupt:
            print(f"\n\n👋 用户中断程序")
        except Exception as e:
            print(f"\n❌ 演示失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    generator = DemoFinancialGenerator()
    generator.run_demo()

if __name__ == "__main__":
    main()
