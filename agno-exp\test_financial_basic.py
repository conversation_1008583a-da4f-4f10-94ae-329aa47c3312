"""
基础财报生成测试
"""

import json
from pathlib import Path
from agno.agent import Agent
from agno.models.ollama import Ollama

def test_basic_generation():
    """测试基础HTML生成功能"""
    
    print("🧪 基础财报生成测试")
    print("=" * 40)
    
    # 1. 创建简单的财报数据
    simple_data = {
        "company": "测试科技公司",
        "period": "2023年度",
        "total_assets": "139亿元",
        "total_revenue": "85亿元", 
        "net_profit": "10.67亿元",
        "debt_ratio": "49.5%"
    }
    
    print("📊 测试数据:")
    print(json.dumps(simple_data, ensure_ascii=False, indent=2))
    
    # 2. 创建简单的Agent
    try:
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
            options={"temperature": 0.1}
        )
        
        agent = Agent(
            model=model,
            instructions="""
            你是一个HTML生成专家。
            根据提供的财报数据，生成一个简单但美观的HTML页面。
            要求：
            1. 使用基础的HTML和CSS
            2. 包含公司名称、报告期间和关键数据
            3. 使用卡片布局展示数据
            4. 添加简单的样式美化
            
            请直接输出完整的HTML代码。
            """,
            markdown=False,
        )
        
        print("✅ Agent创建成功")
        
    except Exception as e:
        print(f"❌ Agent创建失败: {e}")
        return
    
    # 3. 生成HTML
    try:
        print("\n🤖 生成HTML...")
        
        prompt = f"""
        请根据以下财报数据生成一个简单的HTML页面：
        
        {json.dumps(simple_data, ensure_ascii=False, indent=2)}
        
        要求生成一个完整的HTML页面，包含基本的CSS样式。
        """
        
        response = agent.run(prompt)
        html_content = response.content
        
        print("✅ HTML生成成功")
        print(f"📏 内容长度: {len(html_content)} 字符")
        
        # 4. 保存HTML文件
        output_dir = Path("agno-exp/html_reports")
        output_dir.mkdir(exist_ok=True)
        
        output_file = output_dir / "test_basic_report.html"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML文件已保存: {output_file}")
        
        # 5. 显示HTML内容预览
        print("\n📄 HTML内容预览:")
        print("-" * 40)
        print(html_content[:500] + "..." if len(html_content) > 500 else html_content)
        
        return True
        
    except Exception as e:
        print(f"❌ HTML生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_basic_generation()
    
    if success:
        print("\n🎉 测试成功！")
        print("可以在浏览器中打开生成的HTML文件查看效果。")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    main()
