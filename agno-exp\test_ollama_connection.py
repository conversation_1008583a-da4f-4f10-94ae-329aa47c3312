"""
测试Ollama连接
"""

import requests
import json

def test_ollama_direct():
    """直接测试Ollama API"""
    print("🔄 直接测试Ollama API...")
    
    try:
        # 测试基本连接
        url = "http://*************:11434/api/tags"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama连接成功")
            print(f"📋 可用模型: {[m['name'] for m in models.get('models', [])]}")
            
            # 检查目标模型
            model_names = [m['name'] for m in models.get('models', [])]
            if "qwen3:0.6b" in model_names:
                print("✅ qwen3:0.6b 模型可用")
                return True
            else:
                print("❌ qwen3:0.6b 模型不可用")
                print("💡 请运行: ollama pull qwen3:0.6b")
                return False
        else:
            print(f"❌ Ollama连接失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        return False

def test_ollama_generate():
    """测试Ollama生成功能"""
    print("\n🔄 测试Ollama生成功能...")
    
    try:
        url = "http://*************:11434/api/generate"
        data = {
            "model": "qwen3:0.6b",
            "prompt": "请回复'测试成功'",
            "stream": False
        }
        
        response = requests.post(url, json=data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 生成成功: {result.get('response', '')}")
            return True
        else:
            print(f"❌ 生成失败，状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False

def test_agno_ollama():
    """测试Agno的Ollama集成"""
    print("\n🔄 测试Agno的Ollama集成...")
    
    try:
        from agno.models.ollama import Ollama
        from agno.agent import Agent
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            instructions="你是一个测试助手。"
        )
        
        response = agent.run("请回复'Agno集成成功'")
        print(f"✅ Agno集成成功: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ Agno集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 Ollama连接测试")
    print("=" * 50)
    
    # 1. 直接API测试
    api_ok = test_ollama_direct()
    
    if not api_ok:
        print("\n❌ 基本连接失败，无法继续测试")
        return
    
    # 2. 生成测试
    generate_ok = test_ollama_generate()
    
    if not generate_ok:
        print("\n❌ 生成功能失败，无法继续测试")
        return
    
    # 3. Agno集成测试
    agno_ok = test_agno_ollama()
    
    if agno_ok:
        print("\n✅ 所有测试通过！可以使用PDF知识库功能")
    else:
        print("\n❌ Agno集成失败")

if __name__ == "__main__":
    main()
