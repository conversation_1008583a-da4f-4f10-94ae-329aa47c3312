import asyncio

from agno.agent import Agent, RunResponse  # noqa
from agno.models.aws import Claude

agent = Agent(
    model=<PERSON>(id="anthropic.claude-3-5-sonnet-20240620-v1:0"), markdown=True
)

# Get the response in a variable
# run: RunResponse = agent.run("Share a 2 sentence horror story")
# print(run.content)

# Print the response in the terminal
asyncio.run(agent.aprint_response("Share a 2 sentence horror story"))
