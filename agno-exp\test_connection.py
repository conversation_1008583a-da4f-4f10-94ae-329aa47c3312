"""
测试Ollama连接和Agno功能
"""

import sys
from agno.models.ollama import Ollama

def test_ollama_connection():
    """测试Ollama服务连接"""
    print("🔍 测试Ollama连接...")
    
    try:
        # 创建Ollama实例
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        # 获取客户端
        client = model.get_client()
        
        # 测试连接 - 尝试列出模型
        models = client.list()
        print("✅ Ollama连接成功！")
        
        # 检查目标模型是否存在
        model_names = [m['name'] for m in models['models']]
        print(f"📋 可用模型: {model_names}")
        
        if "qwen3:0.6b" in model_names:
            print("✅ qwen3:0.6b 模型可用")
            return True
        else:
            print("❌ qwen3:0.6b 模型不可用")
            print("请运行: ollama pull qwen3:0.6b")
            return False
            
    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        print("请检查:")
        print("1. Ollama服务是否运行在 http://*************:11434")
        print("2. 网络连接是否正常")
        print("3. 防火墙设置")
        return False

def test_simple_chat():
    """测试简单对话"""
    print("\n💬 测试简单对话...")
    
    try:
        from agno.agent import Agent
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            instructions="你是一个友好的AI助手，请用中文回答。"
        )
        
        # 测试简单对话
        response = agent.run("你好，请说'测试成功'")
        print(f"✅ 对话测试成功!")
        print(f"AI回复: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ 对话测试失败: {e}")
        return False

def test_tools():
    """测试工具功能"""
    print("\n🔧 测试工具功能...")
    
    try:
        from agno.agent import Agent
        from datetime import datetime
        
        def get_time():
            """获取当前时间"""
            return f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            tools=[get_time],
            show_tool_calls=True,
            instructions="你是一个AI助手，可以使用工具获取时间。当用户询问时间时，请使用get_time工具。"
        )
        
        # 测试工具调用
        response = agent.run("现在几点了？")
        print(f"✅ 工具测试成功!")
        print(f"AI回复: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        return False

def test_streaming():
    """测试流式输出"""
    print("\n📡 测试流式输出...")
    
    try:
        from agno.agent import Agent
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            instructions="你是一个友好的AI助手。"
        )
        
        print("AI回复: ", end="", flush=True)
        
        # 测试流式输出
        for chunk in agent.run_stream("请简单介绍一下人工智能"):
            print(chunk.content, end="", flush=True)
        
        print("\n✅ 流式输出测试成功!")
        return True
        
    except Exception as e:
        print(f"\n❌ 流式输出测试失败: {e}")
        return False

def test_chinese_support():
    """测试中文支持"""
    print("\n🇨🇳 测试中文支持...")
    
    try:
        from agno.agent import Agent
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            instructions="你是一个中文AI助手，请用标准的中文回答问题。"
        )
        
        # 测试中文对话
        response = agent.run("请用中文介绍一下北京这座城市")
        print(f"✅ 中文支持测试成功!")
        print(f"AI回复: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ 中文支持测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Agno + Ollama 连接测试")
    print("=" * 50)
    
    tests = [
        ("Ollama连接", test_ollama_connection),
        ("简单对话", test_simple_chat),
        ("工具功能", test_tools),
        ("流式输出", test_streaming),
        ("中文支持", test_chinese_support),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！Agno环境配置正确。")
        print("\n📚 可以开始使用以下示例:")
        print("- python agno-exp/base.py")
        print("- python agno-exp/base1.py")
        print("- python agno-exp/base3Pdf.py")
        print("- python agno-exp/start_financial_generator.py")
    else:
        print(f"\n⚠️  有 {len(results) - passed} 项测试失败，请检查配置。")
        print("\n🔧 故障排除建议:")
        print("1. 确保Ollama服务正常运行")
        print("2. 检查模型是否已下载: ollama pull qwen3:0.6b")
        print("3. 验证网络连接")
        print("4. 检查防火墙设置")

if __name__ == "__main__":
    main()
