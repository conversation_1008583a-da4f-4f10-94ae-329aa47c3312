"""
测试Ollama连接和模型可用性
"""

import sys
from agno.models.ollama import Ollama

def test_ollama_connection():
    """测试Ollama服务连接"""
    print("🔍 测试Ollama连接...")
    
    try:
        # 创建Ollama实例
        model = Ollama(
            id="gemma3n:e2b",
            host="http://*************:11434",
        )
        
        # 获取客户端
        client = model.get_client()
        
        # 测试连接 - 尝试列出模型
        models = client.list()
        print(models)
        print("✅ Ollama连接成功！")
        
        # 检查目标模型是否存在
        model_names = [m['model'] for m in models['models']]
        print(f"📋 可用模型: {model_names}")
        
        if "gemma3n:e2b" in model_names:
            print("✅ gemma3n:e2b 模型可用")
            return True
        else:
            print("❌ gemma3n:e2b 模型不可用")
            print("请运行: ollama pull qwen3:0.6b")
            return False

    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        print("请检查:")
        print("1. Ollama服务是否运行在 http://*************:11434")
        print("2. 网络连接是否正常")
        print("3. 防火墙设置")
        return False

def test_simple_chat():
    """测试简单对话"""
    print("\n💬 测试简单对话...")

    try:
        from agno.agent import Agent

        model = Ollama(
            id="gemma3n:e2b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            instructions="你是一个友好的AI助手，请用中文回答。"
        )
        
        # 测试简单对话
        response = agent.run("你好，请说'测试成功'")
        print(f"✅ 对话测试成功!")
        print(f"AI回复: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ 对话测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Agno + Ollama 连接测试")
    print("=" * 50)
    
    # 测试连接
    connection_ok = test_ollama_connection()
    
    if connection_ok:
        # 测试对话
        chat_ok = test_simple_chat()
        
        if chat_ok:
            print("\n🎉 所有测试通过！可以开始使用示例了。")
            print("\n📚 运行示例:")
            print("python agno-exp/base1.py")
            print("python agno-exp/simple_tools_example.py")
            print("python agno-exp/advanced_example.py")
        else:
            print("\n⚠️ 对话测试失败，请检查模型配置。")
    else:
        print("\n❌ 连接测试失败，请先解决连接问题。")

if __name__ == "__main__":
    main()
