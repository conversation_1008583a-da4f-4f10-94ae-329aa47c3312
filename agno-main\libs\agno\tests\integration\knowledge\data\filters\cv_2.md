# <PERSON>
Software Engineer

## Summary:
Experienced full-stack developer with 4 years of experience building RESTful APIs and dynamic web applications.

## Experience:
Software Engineer at NeoCode Labs, Austin, TX (2021 – Present)
- Led migration from monolith to microservices architecture.
- Implemented CI/CD pipelines reducing deployment time by 40%.
Junior Developer at Innovent Solutions, Austin, TX (2019 – 2021)
- Contributed to Angular front-end development.
- Maintained PostgreSQL databases.

## Education:
BSc in Software Engineering, University of Texas at Austin (2015 – 2019)

## Skills
Node.js, Angular, Docker, PostgreSQL, Kubernetes