"""
PDF知识库故障排除脚本
帮助诊断和解决常见问题
"""

import os
import shutil
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    print("-" * 30)
    
    # 检查Python包
    packages = ['agno', 'ollama', 'lancedb', 'pydantic']
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
    
    print()

def check_pdf_file():
    """检查PDF文件"""
    print("📄 检查PDF文件...")
    print("-" * 30)
    
    possible_paths = [
        "./pur.pdf",
        "pur.pdf", 
        "agno-exp/pur.pdf",
        "../pur.pdf"
    ]
    
    found_files = []
    for path in possible_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ 找到: {path} (大小: {size} 字节)")
            found_files.append(path)
        else:
            print(f"❌ 未找到: {path}")
    
    if not found_files:
        print("\n💡 建议:")
        print("1. 确保PDF文件存在")
        print("2. 检查文件名是否正确")
        print("3. 确保文件有读取权限")
    
    print()
    return found_files

def check_ollama_connection():
    """检查Ollama连接"""
    print("🔗 检查Ollama连接...")
    print("-" * 30)
    
    try:
        from agno.models.ollama import Ollama
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        client = model.get_client()
        models = client.list()
        
        print("✅ Ollama连接成功")
        print(f"📋 可用模型: {[m['name'] for m in models['models']]}")
        
        # 检查目标模型
        model_names = [m['name'] for m in models['models']]
        if "qwen3:0.6b" in model_names:
            print("✅ qwen3:0.6b 模型可用")
        else:
            print("❌ qwen3:0.6b 模型不可用")
            print("💡 请运行: ollama pull qwen3:0.6b")
        
        return True
        
    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        print("💡 建议:")
        print("1. 检查Ollama服务是否运行")
        print("2. 检查网络连接")
        print("3. 验证服务地址: http://*************:11434")
        return False
    
    print()

def clean_vector_db():
    """清理向量数据库"""
    print("🧹 清理向量数据库...")
    print("-" * 30)
    
    db_paths = [
        "tmp/lancedb",
        "tmp/lancedb_equipment", 
        "tmp/lancedb_pdf",
        "tmp/simple_pdf_db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            try:
                shutil.rmtree(path)
                print(f"✅ 已删除: {path}")
            except Exception as e:
                print(f"❌ 删除失败 {path}: {e}")
        else:
            print(f"ℹ️  不存在: {path}")
    
    print()

def test_pdf_reading():
    """测试PDF读取功能"""
    print("📖 测试PDF读取...")
    print("-" * 30)
    
    pdf_files = check_pdf_file()
    if not pdf_files:
        print("❌ 没有可用的PDF文件")
        return False
    
    pdf_path = pdf_files[0]
    
    try:
        from agno.knowledge.pdf import PDFReader
        
        reader = PDFReader(chunk=True, chunk_size=500)
        documents = reader.read(pdf=pdf_path)
        
        print(f"✅ PDF读取成功")
        print(f"📊 文档块数: {len(documents)}")
        
        if documents:
            first_doc = documents[0]
            print(f"📝 第一块内容预览: {first_doc.content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF读取失败: {e}")
        return False
    
    print()

def test_simple_agent():
    """测试简单Agent"""
    print("🤖 测试简单Agent...")
    print("-" * 30)
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        model = Ollama(
            id="qwen3:0.6b",
            host="http://*************:11434",
        )
        
        agent = Agent(
            model=model,
            instructions="你是一个友好的AI助手。"
        )
        
        response = agent.run("你好，请说'测试成功'")
        print(f"✅ Agent测试成功")
        print(f"🤖 回复: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent测试失败: {e}")
        return False
    
    print()

def main():
    """主诊断流程"""
    print("🔧 PDF知识库故障排除")
    print("=" * 50)
    
    # 1. 检查环境
    check_environment()
    
    # 2. 检查PDF文件
    pdf_files = check_pdf_file()
    
    # 3. 检查Ollama连接
    ollama_ok = check_ollama_connection()
    
    # 4. 测试PDF读取
    if pdf_files:
        pdf_read_ok = test_pdf_reading()
    else:
        pdf_read_ok = False
    
    # 5. 测试简单Agent
    if ollama_ok:
        agent_ok = test_simple_agent()
    else:
        agent_ok = False
    
    # 6. 清理建议
    print("🛠️  故障排除建议")
    print("-" * 30)
    
    if not pdf_files:
        print("1. ❌ 请确保PDF文件存在且路径正确")
    
    if not ollama_ok:
        print("2. ❌ 请检查Ollama服务连接")
    
    if not pdf_read_ok:
        print("3. ❌ PDF读取有问题，可能是文件损坏或权限问题")
    
    if not agent_ok:
        print("4. ❌ Agent创建失败，检查模型配置")
    
    if pdf_files and ollama_ok and pdf_read_ok and agent_ok:
        print("✅ 所有检查通过！可以尝试运行PDF知识库示例")
        print("\n🚀 建议运行:")
        print("python agno-exp/simple_pdf_example.py")
    else:
        print("\n🔄 如果问题持续，尝试:")
        print("1. 重新安装依赖: pip install -U agno ollama")
        print("2. 重启Ollama服务")
        print("3. 清理向量数据库缓存")
    
    # 询问是否清理
    print("\n🧹 是否清理向量数据库缓存? (y/n): ", end="")
    try:
        choice = input().strip().lower()
        if choice in ['y', 'yes', '是']:
            clean_vector_db()
    except:
        pass

if __name__ == "__main__":
    main()
