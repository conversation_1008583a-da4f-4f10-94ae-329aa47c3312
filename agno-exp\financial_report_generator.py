"""
上市公司财报数据HTML页面生成系统
使用Agno框架和Ollama模型分析Excel财报数据并生成动态HTML页面
"""

import os
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
from textwrap import dedent

from agno.agent import Agent
from agno.models.ollama import Ollama

class FinancialReportGenerator:
    """财报HTML生成器"""
    
    def __init__(self, 
                 input_dir: str = "financial_data",
                 output_dir: str = "html_reports", 
                 prompt_file: str = "financial_prompt.txt",
                 ollama_host: str = "http://*************:11434",
                 model_name: str = "qwen3:0.6b"):
        """
        初始化财报生成器
        
        Args:
            input_dir: 输入Excel文件目录
            output_dir: 输出HTML文件目录
            prompt_file: 提示词文件路径
            ollama_host: Ollama服务地址
            model_name: 使用的模型名称
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.prompt_file = Path(prompt_file)
        self.ollama_host = ollama_host
        self.model_name = model_name
        
        # 创建必要的目录
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化模型和Agent
        self._init_agent()
    
    def _init_agent(self):
        """初始化Agno Agent"""
        try:
            # 配置Ollama模型
            self.model = Ollama(
                id=self.model_name,
                host=self.ollama_host,
                options={
                    "temperature": 0.3,  # 较低温度确保输出稳定
                    "top_p": 0.9,
                    "num_ctx": 8192,  # 增加上下文长度
                }
            )
            
            # 读取提示词
            prompt = self._load_prompt()
            
            # 创建Agent
            self.agent = Agent(
                model=self.model,
                instructions=prompt,
                markdown=False,  # 关闭markdown，直接输出HTML
            )
            
            print("✅ Agent初始化成功")
            
        except Exception as e:
            print(f"❌ Agent初始化失败: {e}")
            raise
    
    def _load_prompt(self) -> str:
        """加载提示词文件"""
        if self.prompt_file.exists():
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            # 如果提示词文件不存在，创建默认提示词
            default_prompt = self._create_default_prompt()
            with open(self.prompt_file, 'w', encoding='utf-8') as f:
                f.write(default_prompt)
            print(f"✅ 已创建默认提示词文件: {self.prompt_file}")
            return default_prompt
    
    def _create_default_prompt(self) -> str:
        """创建默认提示词"""
        return dedent("""\
            # 角色：上市公司财报数据HTML页面生成专家
            ## 简介：
            - 语言：中文
            - 描述：专业的财报数据分析师和HTML动态网页设计专家，擅长创建符合现代设计趋势和技术要求的财报展示页面。

            ## 背景：
            你是一位资深的财务分析师和网页设计专家，专门将上市公司财报数据转化为视觉吸引力强的HTML动态网页。你熟悉各种现代web技术和设计趋势，尤其擅长Bemto Grid布局和GSAP动效。

            ## 目标：
            生成一个完整的、可直接使用的HTML页面，用于展示上市公司财报数据，该页面应符合所有技术和设计要求。

            ## 技术要求：
            1. 使用Bemto Grid布局系统
            2. 集成GSAP动效和Framer Motion
            3. 基于HTML5和TailwindCSS开发
            4. 响应式设计和大小字体对比应用

            ## 设计规范：
            1. 根据公司特性选择适当的背景颜色和主题色调
            2. 应用超大字体和视觉元素突出重点，创造视觉对比
            3. 中英文混排，大字体为主，英文小字点题
            4. 使用简洁的矩形元素进行数据可视化
            5. 高亮色透明效果用于边框，避免不同高亮色互相覆盖
            6. 所有数据图表采用脚注样式，保持主题一致性
            7. 避免使用emoji作为主要图标

            ## 输出格式：
            请直接提供完整的HTML代码，包含所有必要的CSS和JavaScript，确保代码可以直接复制使用并正常运行。代码应包含：
            1. 完整的HTML结构
            2. 内联或外部引用的CSS（包括TailwindCSS）
            3. 必要的JavaScript（包括GSAP和Framer Motion）
            4. CDN引用和其他必要的资源链接

            ## 数据处理要求：
            - 重点关注资产负债表数据
            - 突出显示关键财务指标
            - 计算并展示财务比率
            - 提供同比增长率分析
            - 使用图表可视化趋势数据

            ## 初始化：
            作为上市公司财报数据HTML页面生成专家，我已准备好为您创建一个完整的HTML页面。请提供财报数据，我将直接为您生成可用的HTML代码。
        """)
    
    def read_excel_files(self) -> List[Dict[str, Any]]:
        """读取输入目录下的所有Excel文件"""
        excel_files = []
        
        # 支持的Excel文件扩展名
        excel_extensions = ['.xlsx', '.xls']
        
        for file_path in self.input_dir.iterdir():
            if file_path.suffix.lower() in excel_extensions:
                try:
                    print(f"📄 正在读取: {file_path.name}")
                    
                    # 读取Excel文件的所有工作表
                    excel_data = pd.read_excel(file_path, sheet_name=None)
                    
                    file_info = {
                        'filename': file_path.name,
                        'filepath': str(file_path),
                        'sheets': {}
                    }
                    
                    # 处理每个工作表
                    for sheet_name, df in excel_data.items():
                        # 转换为字符串格式，便于模型处理
                        sheet_data = df.to_string(index=False, max_rows=100)
                        file_info['sheets'][sheet_name] = {
                            'data': sheet_data,
                            'shape': df.shape,
                            'columns': df.columns.tolist()
                        }
                    
                    excel_files.append(file_info)
                    print(f"✅ 成功读取: {file_path.name} ({len(excel_data)} 个工作表)")
                    
                except Exception as e:
                    print(f"❌ 读取文件失败 {file_path.name}: {e}")
        
        return excel_files
    
    def generate_html_report(self, excel_data: Dict[str, Any]) -> str:
        """为单个Excel文件生成HTML报告"""
        try:
            # 构建发送给模型的数据
            data_summary = f"文件名: {excel_data['filename']}\n\n"
            
            for sheet_name, sheet_info in excel_data['sheets'].items():
                data_summary += f"工作表: {sheet_name}\n"
                data_summary += f"数据维度: {sheet_info['shape']}\n"
                data_summary += f"列名: {', '.join(sheet_info['columns'])}\n"
                data_summary += f"数据内容:\n{sheet_info['data'][:2000]}...\n\n"  # 限制长度
            
            # 发送给模型进行分析和HTML生成
            prompt = f"""
            请分析以下财报数据并生成完整的HTML页面：

            {data_summary}

            请根据上述数据生成一个完整的HTML页面，包含所有必要的CSS和JavaScript代码。
            """
            
            print(f"🤖 正在生成HTML报告: {excel_data['filename']}")
            response = self.agent.run(prompt)
            
            return response.content
            
        except Exception as e:
            print(f"❌ HTML生成失败: {e}")
            return None
    
    def save_html_report(self, html_content: str, filename: str) -> str:
        """保存HTML报告到文件"""
        try:
            # 生成输出文件名
            base_name = Path(filename).stem
            output_file = self.output_dir / f"{base_name}_report.html"
            
            # 保存HTML内容
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML报告已保存: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 保存HTML失败: {e}")
            return None
    
    def process_all_files(self):
        """处理所有Excel文件"""
        print("🚀 开始处理财报数据...")
        print("=" * 60)
        
        # 读取所有Excel文件
        excel_files = self.read_excel_files()
        
        if not excel_files:
            print(f"❌ 在目录 {self.input_dir} 中未找到Excel文件")
            return
        
        print(f"📊 找到 {len(excel_files)} 个Excel文件")
        
        # 处理每个文件
        for i, excel_data in enumerate(excel_files, 1):
            print(f"\n📈 处理文件 {i}/{len(excel_files)}: {excel_data['filename']}")
            print("-" * 40)
            
            # 生成HTML报告
            html_content = self.generate_html_report(excel_data)
            
            if html_content:
                # 保存HTML文件
                output_file = self.save_html_report(html_content, excel_data['filename'])
                if output_file:
                    print(f"🎉 成功生成报告: {output_file}")
            else:
                print(f"❌ 跳过文件: {excel_data['filename']}")
        
        print(f"\n✅ 处理完成！HTML报告保存在: {self.output_dir}")

def main():
    """主函数"""
    print("📊 上市公司财报数据HTML页面生成系统")
    print("=" * 60)
    
    # 创建生成器实例
    generator = FinancialReportGenerator(
        input_dir="agno-exp/financial_data",
        output_dir="agno-exp/html_reports",
        prompt_file="agno-exp/financial_prompt.txt"
    )
    
    # 处理所有文件
    generator.process_all_files()

if __name__ == "__main__":
    main()
